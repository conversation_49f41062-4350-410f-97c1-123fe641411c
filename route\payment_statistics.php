<?php
use think\facade\Route;

// 收款统计相关路由
Route::group('payment-statistics', function () {
    // 写入收款/退款数据
    Route::post('write', 'PaymentStatistics/write');
    
    // 获取当日数据
    Route::get('today', 'PaymentStatistics/today');
    
    // 获取历史数据列表
    Route::get('list', 'PaymentStatistics/list');
    
    // 手动同步数据（管理员使用）
    Route::post('sync', 'PaymentStatistics/syncData');
    
    // 获取公司列表
    Route::get('companies', 'PaymentStatistics/companies');
});
