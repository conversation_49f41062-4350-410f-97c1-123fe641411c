<?php


namespace app\service;


use app\BaseService;
use app\ErrorCode;
use app\model\Course as CourseModel;
use app\model\CrossLockOrder;
use app\model\Order as OrderModel;
use app\service\Additional as AdditionalService;
use app\service\AfterSales as AfterSalesService;
use app\service\elasticsearch\ElasticSearchService;
use app\service\es\Es;
use app\service\Notify as NotifyService;
use app\service\OrderInvoice as OrderInvoiceService;
use app\service\Push as PushService;
use think\Exception;
use think\facade\Db;
use think\facade\Log;
use think\facade\Validate;

class Order extends BaseService
{
    /**
     * Description:全部订单列表
     * Author: zrc
     * Date: 2021/9/6
     * Time: 10:19
     * @param $requestparams
     */
    public function list($requestparams)
    {
        $page       = !empty($requestparams['page']) ? $requestparams['page'] : 1;
        $limit      = !empty($requestparams['limit']) ? $requestparams['limit'] : 10;
        $params     = $requestparams;
        $orderModel = new OrderModel();
        $orderLists = $orderModel->getOrderList($params, $page, $limit);
        return $orderLists;
    }

    /**
     * Description:公共创建订单
     * Author: zrc
     * Date: 2021/8/9
     * Time: 12:23
     * @param $requestparams
     */
    public function createOrder($requestparams, $countMoney = null, $userInfo)
    {
        $params = $requestparams;
        if ($countMoney === null) {
            $data              = array(
                'items_info'        => $params['items_info'],
                'coupon_id'         => intval($params['coupon_id']),
                'is_use_coupon'     => empty($params['coupon_id']) ? 0 : 1,
                'province_id'       => intval($params['province_id']),
                'district_id'       => intval($params['district_id']),
                'express_coupon_id' => intval($params['express_coupon_id']),
                'submit_type'       => intval($params['submit_type']),
                'city_id'           => intval($params['city_id']),
                'special_type'      => intval($params['special_type']),
                'is_scan_code'      => isset($params['is_scan_code']) ? $params['is_scan_code'] : 0,
                'activity_id'       => $params['activity_id'] ?? 0,
            );
            $data['longitude'] = isset($params['longitude']) ? $params['longitude'] : '';
            $data['latitude']  = isset($params['latitude']) ? $params['latitude'] : '';
            $header            = [
                "content-type: application/json",
                "vinehoo-client: orders",
                "vinehoo-uid:" . $params['uid']
            ];
        }

        $all_periods = implode(',', array_unique(array_column($params['items_info'], 'period')));;

        Db::startTrans();
        try {
            if ($countMoney === null) {
                //主、子订单金额计算
                $countMoney = httpPostString(env('ITEM.CALC-ORDERS_PRICE') . '/ordersPrice/v3/order/calculateOrderMoney', json_encode($data), $header);
                if (empty($countMoney)) $this->throwError('订单金额计算失败！');
                if ($countMoney['error_code'] != 0) $this->throwError($countMoney['error_msg']);
                $countMoney = $countMoney['data'];
                if ($countMoney['payment_amount'] <= 0) $this->throwError('订单金额计算异常，请重试！');
                // 专题期数
                $params['activity_package'] = $countMoney['activity_package'] ?? [];
            }
            $sub_order_nos = $countMoney['sub_order_nos'] ?? [];

            if ($params['submit_type'] == 2 && $params['is_replace_pay'] == 0) {
                if (empty($params['realname']) || empty($params['id_card_no'])) $this->throwError('真实姓名、身份证必填');
                $params['id_card_no'] = strtoupper($params['id_card_no']);
                if (get_age_by_id_card($params['id_card_no']) < 18) $this->throwError('根据《中华人民共和国未成年人保护法》的相关规定，不得向未成年用户销售含酒精类商品。由于您未成年，我们无法为您服务。');
                $toYear               = date('Y', time());
                $cross_predict_time   = $countMoney['items_info'][0]['predict_time'] ?? date('Y-m-d');
                $cross_predict_year   = date('Y', strtotime($cross_predict_time));
                $blacklist            = Db::name('cross_quota_blacklist')->field('type,year')->where(array('id_card_no' => $params['id_card_no']))->find();

                $is_intercept = false; //是否拦截创建订单
                if ($blacklist['type'] == 2) $is_intercept = true;
                if ($toYear == $cross_predict_year) {
                    if ($blacklist['type'] == 1 && $blacklist['year'] == $toYear) $is_intercept = true;
                } else {
                    //查询该年份是否超额 该年份以往额度 + 这个订单额度是否 超过 26000
                    $year_start             = "{$cross_predict_year}-01-01 00:00:00";
                    $year_end               = date('Y-m-d H:i:s', strtotime($year_start . " +1year -1 second"));
                    $enc_id_card_no         = cryptionDeal(1, [$params['id_card_no']], $params['uid'], '前端用户')[$params['id_card_no']];
                    $history_payment_amount = Db::name('cross_order')
                        ->where([
                            ['refund_status', '<>', 2],
                            ['sub_order_status', 'in', [1, 2, 3]],
                            ['id_card_no', '=', $enc_id_card_no],
                        ])->whereBetweenTime('predict_time', $year_start, $year_end)
                        ->sum('payment_amount');
                    $total_year_amount      = bcadd($countMoney['payment_amount'], $history_payment_amount, 2);
                    if ($total_year_amount > 26000) $is_intercept = true;
                }

                if ($is_intercept) {
                    //套餐ID获取商品套餐信息
                    $items_info  = $params['items_info'];
                    $packageInfo = esGetOne($items_info[0]['package_id'], 'vinehoo.periods_set');
                    if (isset($packageInfo['price'])) {
                        //跨境下单拦截记录收集
                        $intercept_record = array(
                            'uid'            => $params['uid'],
                            'period'         => $items_info[0]['period'],
                            'package_id'     => $items_info[0]['package_id'],
                            'order_qty'      => $items_info[0]['nums'],
                            'payment_amount' => $packageInfo['price'],
                            'realname'       => $params['realname'],
                            'id_card_no'     => $params['id_card_no'],
                            'note'           => '跨境电子商务年度个人额度不足' . $cross_predict_year,
                            'created_time'   => time(),
                        );
                        Db::name('cross_order_intercept_record')->insert($intercept_record);
                    }
                    $this->throwError('跨境电子商务年度个人额度不足' . $cross_predict_year);
                }
            }

            //主订单数据写入
            $main_order_no = $params['main_order_no'];
            //用户信息加密处理
            $consignee = trim($params['consignee']);
            $phone     = trim($params['consignee_phone']);
            $encrypt   = cryptionDeal(1, [$consignee, $phone], $params['uid'], '前端用户');
            $consignee = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
            $phone     = isset($encrypt[$phone]) ? $encrypt[$phone] : '';
            $mainData  = array(
                'uid'               => $params['uid'],
                'special_type'      => $params['is_deposit'],
                'main_order_no'     => $main_order_no,
                'main_order_status' => 0,
                'payment_amount'    => $countMoney['payment_amount'],
                'cash_amount'    => $countMoney['payment_amount'],
                'province_id'       => $params['province_id'],
                'city_id'           => $params['city_id'],
                'district_id'       => $params['district_id'],
                'address'           => trim($params['address']),
                'consignee'         => $consignee,
                'consignee_phone'   => $phone,
                'created_time'      => time()
            );
            if ($params['submit_type'] == 4) {
                $mainData['order_type'] = implode(',', array_unique(array_column($countMoney['items_info'], 'periods_type')));//混合支付
            } else {
                $mainData['order_type'] = $params['submit_type'];
            }
            if (isset($params['payment_method'])) {
                $mainData['payment_method'] = $params['payment_method'];
            }
            $orderIdMain = Db::name('order_main')->insertGetId($mainData);
            if (empty($orderIdMain)) $this->throwError('主订单写入失败！');

            // 所有期数
            $periods = array_unique(array_column($countMoney['items_info'], 'period'));
            //有来源标识进行记录
            if (!empty($params['source_platform']) && !empty($params['source_event'])) {
                # 验证什么值得买来源标识
                $source_event = (new \app\model\OrderZdmRecord())->VerifySourceEvent($params['source_event'], $periods);
                //售前分享除外，售前分享支付回调单独处理
                if (!empty($source_event) && $source_event != 'share') {
                    $sourceData = array(
                        'source_platform' => $params['source_platform'],
                        'source_event'    => $source_event,
                        'source_user'     => $params['source_user'],
                        'main_order_id'   => $orderIdMain,
                        'order_type'      => $mainData['order_type'],
                        'created_time'    => time()
                    );
                    Db::name('order_source_log')->insert($sourceData);
                    if ($source_event == 'sf-express') {
                        $bjsp_type_ids = Db::table('vh_wiki.vh_product_type')
                            ->whereOr('code', 19)
                            ->whereOr('pid', 3)
                            ->column('id');
                        foreach ($countMoney['items_info'] as &$cmii) {
                            if ($cmii['express_type'] != 3) {
                                $cii_pkg      = Es::name(Es::PERIODS_PACKAGE)->where([['_id', '==', $cmii['package_id']]])->field('associated_products')->find();
                                $cipkg_pids   = array_values(array_unique(array_column(json_decode($cii_pkg['associated_products'] ?? '', true) ?? [], 'product_id')));
                                $ciipkg_pnums = Db::table('vh_wiki.vh_products')->where('id', 'in', $cipkg_pids)
                                    ->where('product_type', 'in', $bjsp_type_ids)
                                    ->count();
                                if ($ciipkg_pnums == 0) {
                                    $cmii['express_type'] = 13;
                                }
                            }
                        }
                    }
                }
            }
            //子订单数据写入
            //订单提交类型：0-闪购 1-秒发 2-跨境 3-尾货 4-闪购+秒发混合
            $subdatas = [];
            if ($params['submit_type'] == 0) {
                $flashService = new Flash();
                $subdatas     = $flashService->createSubOrder($countMoney['items_info'], $orderIdMain, $params, $sub_order_nos);
            } else if ($params['submit_type'] == 1) {
                $secondService = new Second();
                $subdatas      = $secondService->createSubOrder($countMoney['items_info'], $orderIdMain, $params, $sub_order_nos);
            } else if ($params['submit_type'] == 2) {
                $crossService = new Cross();
                $subdatas     = $crossService->createSubOrder($countMoney['items_info'], $orderIdMain, $params, $sub_order_nos);
            } else if ($params['submit_type'] == 3) {
                $tailService = new Tail();
                $subdatas    = $tailService->createSubOrder($countMoney['items_info'], $orderIdMain, $params, $sub_order_nos);
            } else if ($params['submit_type'] == 4) {
                $subdatas = $this->createSubOrder($countMoney['items_info'], $orderIdMain, $params, $sub_order_nos);
            } else if ($params['submit_type'] == 9) {
                $tailService = new MerchantSecond();
                $subdatas    = $tailService->createSubOrder($countMoney['items_info'], $orderIdMain, $params, $sub_order_nos);
            }

            #region 暂存时间
            try {
                $ts_list = [];
                foreach ($params['items_info'] as $item_info) {
                    if ((($item_info['is_ts'] ?? 0) == 1) && !empty($item_info['ts_time'])) {
                        $ts_list["{$item_info['package_id']}_{$item_info['nums']}_{$item_info['is_ts']}"] = strtotime($item_info['ts_time']);
                    }
                }
                $ts_arr = [];
                foreach ($subdatas as $tsubdata) {
                    $tk = "{$tsubdata['package_id']}_{$tsubdata['order_qty']}_{$tsubdata['is_ts']}";
                    if (!empty($ts_list[$tk])) {
                        $ts_arr[] = [
                            'sub_order_no' => $tsubdata['sub_order_no'],
                            'order_type'   => $tsubdata['order_type'],
                            'ts_time'      => $ts_list[$tk],
                            'admin_id'     => 0,
                            'uid'          => $tsubdata['uid'],
                            'remarks'      => '用户下单选择暂存',
                            'created_time' => time(),
                            'update_time'  => time(),
                        ];
                    }
                }
                Db::name('order_ts')->insertAll($ts_arr);
            } catch (\Exception $e) {
                Log::write("订单设置暂存时间失败:" . $e->getMessage() . ' _ ' . json_encode($params));
            }
            #endregion 暂存时间

//            $force_merge_data    = $tf_merge_data    = [];
//            $subdatas_periods    = array_column($subdatas, 'period');
//            $force_merge_periods = Db::table('vh_commodities.vh_periods_add_purchase')->where('period', 'in', $subdatas_periods)->where('is_force_merge', 1)->column('period');
//            if (!empty($force_merge_periods)) {
//                $erp_ids = Db::table('vh_commodities.vh_periods_product_inventory')
//                    ->where('period', 'in', $subdatas_periods)->column('id,period,erp_id');
//                $erp_ids = array_column($erp_ids, 'erp_id', 'period');
//                foreach ($force_merge_periods as $fmp) {
//                    $fmp_erp_id = $erp_ids[$fmp] ?? null;
//                    if ($fmp_erp_id === null) continue;
//                    foreach ($subdatas as $sd) {
//                        $tf_merge_data = [$sd['sub_order_no']];
//                        if (in_array($sd['period'], $force_merge_periods)) continue;
//                        $sd_erp_id = $erp_ids[$sd['period']] ?? null;
//                        if ($sd_erp_id === null) continue;
//                        if ($sd_erp_id == $fmp_erp_id) {
//                            foreach ($subdatas as $msd) {
//                                if ($msd['period'] == $fmp) {
//                                    $tf_merge_data[] = $msd['sub_order_no'];
//                                }
//                            }
//                            foreach ($tf_merge_data as $o1) {
//                                foreach ($tf_merge_data as $o2) {
//                                    $force_merge_data[] = ['sub_order_no' => $o1, 'merge_order_no' => $o2,];
//                                }
//                            }
//                        }
//                    }
//                }
//            }
//            if (!empty($force_merge_data)) {
//                Db::name('merge_order')->insertAll($force_merge_data);
//            }

            // 记录专题期数
            // if (!empty($params['activity_periods'])) {
                $order_extend = [];
                foreach ($subdatas as $v) {
                    if (!empty($params['activity_package'])) {
                        $activity_package = array_column($params['activity_package'], null, 'package_id');
                    }
                    $order_extend[] = [
                        'sub_order_no'           => $v['sub_order_no'],
                        'order_type'             => $v['order_type'],
                        'activity_package_price' => !empty($activity_package[$v['package_id']]['price']) ? $activity_package[$v['package_id']]['price'] : 0,
                        'activity_periods'       => !empty($activity_package[$v['package_id']]['activity_periods']) ? $activity_package[$v['package_id']]['activity_periods'] : '',
                        'user_attribute'         => !empty($userInfo['user_attribute']) ? intval($userInfo['user_attribute']) : 0,
                        'created_time'           => $v['created_time'],
                        'update_time'            => $v['created_time'],
                    ];
                }
                Db::name('sub_order_extend')->insertAll($order_extend);
            // }

            //购物车提交删除购物车数据
            if ($params['is_cart'] == 1) {
                foreach ($params['items_info'] as &$items) {
                    Db::name('shopping_cart')->where(['uid' => $params['uid'], 'period' => $items['period'], 'package_id' => $items['package_id']])->delete();
                }
            }
            //修改订单日志记录
            $addLog = Db::name('order_deal_log')->where(['main_order_no' => $main_order_no])->update(['create_order_status' => 1]);
            if (empty($addLog)) $this->throwError('订单日志数据写入失败');
            //优惠券、快递优惠券使用
            if ($params['coupon_id'] != 0 || $params['express_coupon_id'] != 0) {
                $couponArr = [];
                if ($params['coupon_id'] != 0) {
                    $couponArr[] = ['id' => $params['coupon_id'], 'sub_order_no' => $main_order_no];
                }
                if ($params['express_coupon_id'] != 0) {
                    $couponArr[] = ['id' => $params['express_coupon_id'], 'sub_order_no' => $main_order_no];
                }
                $couponPushData = array(
                    'type'    => 1,
                    'coupons' => $couponArr,
                );
                $header         = array(
                    "content-type: application/json",
                    "vinehoo-client: orders",
                    "vinehoo-uid: " . $params['uid'],
                );
                $pushCoupon     = httpPostString(env('ITEM.COUPON_URL') . '/coupon/v3/couponissue/change', json_encode($couponPushData), $header);
                if ($pushCoupon['error_code'] != 0) $this->throwError('使用优惠券失败！');
            }
            Db::commit();
            //检测是否直播商品
            $is_live        = 0;
            $issetLiveGoods = array_column($params['items_info'], 'period');
            $liveCount      = Db::table('vh_minilive.vh_goods')->where([['period_id', 'in', $issetLiveGoods]])->count();
            if ($liveCount > 0) $is_live = 1;
            if ($params['is_replace_pay'] == 1) {
                //添加超时任务
                $pushData    = array(
                    'namespace' => "orders",
                    'key'       => "replace_" . $main_order_no,
                    'data'      => base64_encode(json_encode(['main_order_no' => $main_order_no])),
                    'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/order/timeOutOrderDeal',
                    'timeout'   => env('ORDERS.replace_pay_time_out_m'),
                );
                $dealReplace = $this->httpPost(env('ITEM.TIMEING_SERVICE_URL') . '/services/v3/timing/add', $pushData);
                //任务日志
                $taskLog = '代付超时任务日志：' . $main_order_no . '请求参数：' . json_encode($pushData, JSON_UNESCAPED_UNICODE) . '返回参数：' . json_encode($dealReplace, JSON_UNESCAPED_UNICODE);
                file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'timingLog' . '.log', $taskLog . PHP_EOL, FILE_APPEND);
                //返回前端数据
                $backParams = array(
                    'main_order_no' => $params['main_order_no'],
                    'countdown'     => env('ORDERS.replace_pay_time_out'),
                    'created_time'  => time()
                );
            } else if (isset($params['payment_method'])) {
                //添加超时任务
                $pushData     = array(
                    'namespace' => "orders",
                    'key'       => "transfer_" . $main_order_no,
                    'data'      => base64_encode(json_encode(['main_order_no' => $main_order_no])),
                    'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/order/timeOutOrderDeal',
                    'timeout'   => env('ORDERS.transfer_pay_time_out_h'),
                );
                $dealTransfer = $this->httpPost(env('ITEM.TIMEING_SERVICE_URL') . '/services/v3/timing/add', $pushData);
                //任务日志
                $taskLog = '对公转账超时任务日志：' . $main_order_no . '请求参数：' . json_encode($pushData, JSON_UNESCAPED_UNICODE) . '返回参数：' . json_encode($dealTransfer, JSON_UNESCAPED_UNICODE);
                file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'timingLog' . '.log', $taskLog . PHP_EOL, FILE_APPEND);
                //返回前端数据
                $transfer_info = [];
                switch ($params['payment_method']) {
                    case 10://线上电汇
                        $transfer_info['illustrate'] = Db::name('order_text')->where(['type' => 18])->value('content');
                        break;
                    case 11://线下转账
                        $collecting_company                   = Db::name('collecting_company')->where(['id' => $params['payee_merchant_id']])->find();
                        $transfer_info['illustrate']          = Db::name('order_text')->where(['type' => 19])->value('content');
                        $transfer_info['transfer_name']       = isset($collecting_company['transfer_name']) ? $collecting_company['transfer_name'] : '';
                        $transfer_info['transfer_account']    = isset($collecting_company['transfer_account']) ? $collecting_company['transfer_account'] : '';
                        $transfer_info['transfer_bank']       = isset($collecting_company['transfer_bank']) ? $collecting_company['transfer_bank'] : '';
                        $transfer_info['transfer_interbank']  = isset($collecting_company['transfer_interbank']) ? $collecting_company['transfer_interbank'] : '';
                        $transfer_info['transfer_remittance'] = $main_order_no;
                        $transfer_info['describe']            = Db::name('order_text')->where(['type' => 20])->value('content');
                        break;
                }
                $backParams = array(
                    'main_order_no' => $params['main_order_no'],
                    'countdown'     => env('ORDERS.transfer_pay_time_out'),
                    'created_time'  => time(),
                    'transfer_info' => $transfer_info
                );
            } else if ($is_live == 1) {
                //创建超时队列
                $pushData = array(
                    'exchange_name' => 'orders',
                    'routing_key'   => 'live.order.timeout',
                    'data'          => base64_encode(json_encode(['main_order_no' => $main_order_no]))
                );
                httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                //返回前端数据
                $backParams = array(
                    'main_order_no' => $params['main_order_no'],
                    'countdown'     => env('ORDERS.LIVE_ORDER_TIMEOUT'),
                    'created_time'  => time()
                );
            } else if (in_array($all_periods, ["142927", "143361"])) {
                //返回前端数据
                $backParams = array(
                    'main_order_no' => $params['main_order_no'],
                    'countdown'     => env('ORDERS.SPECIAL_PAY_TIME_OUT'),
                    'created_time'  => time()
                );
            } else {
                //创建超时队列
                $pushData = array(
                    'exchange_name' => 'orders',
                    'routing_key'   => 'orders_timeout',
                    'data'          => base64_encode(json_encode(['main_order_no' => $main_order_no]))
                );
                httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                //返回前端数据
                $backParams = array(
                    'main_order_no' => $params['main_order_no'],
                    'countdown'     => env('ORDERS.pay_time_out'),
                    'created_time'  => time()
                );
            }
            return $backParams;
        } catch (\Exception $e) {
            Db::rollback();
            //退还库存
            $stock_param = Db::name('order_deal_log')->where(['main_order_no' => $params['main_order_no']])->value('stock_param');
            if (!empty($stock_param)) {
                $stock_param = json_decode($stock_param, true);
                if (($stock_param['groupid'] == 0 && $stock_param['group_status'] == 0) || $stock_param['groupid'] > 0) {
                    curlRequest(env('ITEM.VINEHOO_INVENTORY_INOUT_URL') . '/inventory_service/v3/inventory/inc', json_encode($stock_param, true));
                }
            }
            $this->throwError($e->getMessage());
        }
    }

    /**
     * Description:闪购+秒发创建子订单
     * Author: zrc
     * Date: 2021/8/13
     * Time: 13:43
     * @param $sub_order_data
     * @param $orderIdMain
     * @param $params
     */
    public function createSubOrder($sub_order_data, $orderIdMain, $params, &$sub_order_nos = [])
    {
        //子订单数据写入
        $datas = $items = [];
        foreach ($sub_order_data as &$v) {
            if ($v['periods_type'] == 0) {
                $table = 'flash_order';
            }
            if ($v['periods_type'] == 1) {
                $table   = 'second_order';
                $items[] = array(
                    'genre'         => 'second_goods',
                    'feedback_type' => 'buy',
                    'item_id'       => $v['period'],
                );
            }
            if ($v['periods_type'] == 9) {
                $table = 'merchant_second_order';
            }
            $temp_son = array_shift($sub_order_nos);
            if ($temp_son) {
                $sub_order_no = env('ORDERS.ORDER_SON') . $temp_son;
            } else {
                $sub_order_no = creatOrderNo(env('ORDERS.ORDER_SON'), $params['uid']);
            }
            $subData = array(
                'uid'                    => $params['uid'],
                'sub_order_no'           => $sub_order_no,
                'sub_order_status'       => 0,
                'main_order_id'          => $orderIdMain,
                'period'                 => $v['period'],
                'package_id'             => $v['package_id'],
                'special_type'           => $params['special_type'],
                'special_price'          => $v['special_price'],
                'order_from'             => $params['order_from'],
                'order_qty'              => $v['nums'],
                'payment_amount'         => $v['goods_money'],
                'cash_amount'            => $v['goods_money'],
                'preferential_reduction' => $v['discounted_price'],
                'express_fee'            => $v['express_fee'],
                'money_off_split_value'  => $v['money_off_split_value'],
                'coupon_id'              => $params['coupon_id'],
                'coupon_split_value'     => $v['coupon_split_value'],
                'invoice_progress'       => $params['invoice_progress'],
                'invoice_id'             => $params['invoice_id'],
                'express_type'           => $v['express_type'],
                'express_number'         => '',
                'is_ts'                  => $v['is_ts'],
                'created_time'           => time(),
                'express_coupon_id'      => $params['express_coupon_id'],
                'warehouse_code'         => $v['erp_id'],
            );
            if ($v['periods_type'] == 0) {
                $subData['order_type']          = 0;
                $subData['predict_time']        = strtotime(predictTimeDeal(strtotime($v['predict_time']), 0, $v['express_type'], $v['erp_id']));
                $subData['is_original_package'] = $v['is_original_package'] == 1 ? 1 : 0;
            }
            if ($v['periods_type'] == 1) {
                $subData['order_type']          = 1;
                $subData['predict_time']        = strtotime(predictTimeDeal(strtotime($v['predict_time']), 1, $v['express_type'], $v['erp_id']));
                $subData['is_original_package'] = $v['is_original_package'] == 1 ? 1 : 0;
            }
            if ($v['periods_type'] == 9) {
                $subData['order_type']      = 9;
                $subData['predict_time']    = $v['predict_delivery_time'];
                $subData['express_type']    = 0;
                $subData['product_channel'] = $v['product_channel'];
                $subData['merchant_id']     = $v['supplier_id'];
                $subData['push_wms_status'] = 3;
                //发货点、提货人处理
                foreach ($params['items_info'] as $val) {
                    if ($v['package_id'] == $val['package_id']) {
                        $subData['delivery_store_id']   = $val['delivery_store_id'];
                        $subData['delivery_store_name'] = $val['delivery_store_name'];
                        $subData['merchant_id']         = $val['supplier_id'];
                        $subData['delivery_method']     = $val['express_type'];
                        if ($v['express_type'] == 3) {
                            $subData['delivery_consignee']       = trim($val['delivery_consignee']);
                            $subData['delivery_consignee_phone'] = trim($val['delivery_consignee_phone']);
                        }
                    }
                }
            }
            //加购套餐处理
            if ($v['is_add_purchase'] == 1) $subData['special_type'] = 3;

            $exists_unpay_order_num = Db::name($table)->where([
                'uid'              => $params['uid'],
                'package_id'       => $v['package_id'],
                'sub_order_status' => 0,
                'is_delete'        => 0,
            ])->count();
            if ($exists_unpay_order_num) $this->throwError('您已有该商品的待支付订单，请先处理。');

            $addSubOrder = Db::name($table)->insert($subData);
            if (empty($addSubOrder)) $this->throwError('子订单写入失败');
            //对值得买订单进行进行记录
            $subData['periods_type'] = $v['periods_type'];
            (new \app\model\OrderZdmRecord())->addRecord($subData, $params);
            //暂存订单记录日志方便客服后续排查暂存情况
            if ($v['is_ts'] == 1) {
                Db::name('order_remarks')->insert(['sub_order_no' => $subData['sub_order_no'], 'admin_id' => -1, 'remarks' => '创建订单用户选择暂存', 'created_time' => time()]);
            }
            $datas[] = $subData;
        }
        //秒发商品反馈
        if (!empty($items)) {
            curlRequest(env('ITEM.COMMODITIES_URL') . '/commodities/v3/userPortrait/batchFeedback', json_encode(['items' => $items], true), ['vinehoo-uid:' . $params['uid']], 'POST');
        }
        return $datas;
    }

    /**
     * Description:获取15秒未支付未取消订单
     * Author: zrc
     * Date: 2021/8/14
     * Time: 10:40
     */
    public function getUnpaidOrder()
    {
        $orderModel = new OrderModel();
        $result     = $orderModel->getUnpaidOrder();
        return $result;
    }

    /**
     * Description:公共获取订单详情
     * Author: zrc
     * Date: 2021/8/14
     * Time: 14:37
     * @param $requestparams
     */
    public function orderDetail($requestparams)
    {


        $params = $requestparams;
        if (strpos($params['order_no'], 'VHM') !== false || strpos($params['order_no'], 'WYM') !== false) {
            $order_type = Db::name('order_main')->where(['main_order_no' => $params['order_no']])->value('order_type');
            if (!isset($order_type)) $this->throwError('未获取到订单类型');
            $config_order_type = config('config')['order_type'];//订单频道获取
            $field             = 'om.payment_amount as main_payment_amount,om.cash_amount as main_cash_amount,so.*';
            $orderDetail       = Db::name('order_main')->alias('om')
                ->leftJoin("{$config_order_type[intval($order_type)]['table']} so", 'so.main_order_id = om.id')
                ->field($field)
                ->where(['om.main_order_no' => $params['order_no']])
                ->find();
            if (empty($orderDetail)) $this->throwError('未获取到订单详情');
            //支付倒计时
            if ($order_type == 2 && isset($orderDetail['is_replace_pay']) && $orderDetail['is_replace_pay'] == 1) {
                $time = env('ORDERS.replace_pay_time_out') - (time() - $orderDetail['created_time']);
            } else if (strpos($orderDetail['sub_order_no'], 'VHG') !== false) {
                $time = intval(env('ORDERS.AFTER_SALES_ORDER_TIMEOUT')) * 60 - (time() - $orderDetail['created_time']);
            } else {
                $time = env('ORDERS.pay_time_out') - (time() - $orderDetail['created_time']);
            }
            $orderDetail['countdown'] = $time > 0 ? $time : 0;
            return $orderDetail;
        } else if (strpos($params['order_no'], 'VHP') !== false) {
            $winePartyOrder = $this->httpGet(env('ITEM.WINEPARTY_URL') . '/wineparty/v3/order/getOrderInfo', ['main_order_no' => $params['order_no']]);
            if ($winePartyOrder['error_code'] != 0) $this->throwError('未获取到订单详情');
            $orderDetail = array(
                'uid'                 => isset($winePartyOrder['data']['uid']) ? $winePartyOrder['data']['uid'] : 0,
                'main_order_no'       => $winePartyOrder['data']['main_order_no'],
                'main_payment_amount' => $winePartyOrder['data']['payment_amount'],
                'main_cash_amount' => $winePartyOrder['data']['payment_amount'],
                'main_order_status'   => $winePartyOrder['data']['main_order_status'],
                'order_type'          => 5,
                'created_time'        => $winePartyOrder['data']['created_time'],
                'payment_time'        => $winePartyOrder['data']['payment_time'],
            );
            return $orderDetail;
        } else if (strpos($params['order_no'], 'VHL') !== false) {
            $card_order = Db::table('vh_gift_card.vh_recharge_order')->where('order_no', $params['order_no'])->find();
            if (empty($card_order)) $this->throwError('未获取到订单详情');


            $orderDetail = array(
                'uid'                 => $card_order['uid'],
                'main_order_no'                 => $card_order['order_no'],
                'order_type'                    => 60,
                'main_order_status'                 => $card_order['order_status'],
                'sub_order_status'                 => $card_order['order_status'],
                'countdown'                 => max(($card_order['created_time'] + 300) - time(), 0),
                'group_id'                 => "",
                'main_payment_amount' => $card_order['payment_amount'],
                'main_cash_amount' => $card_order['payment_amount'],
            );
            return $orderDetail;
        } else {
            $where[] = ['sub_order_no.keyword' => $params['order_no']];
        }
        $source = [];
        if (!empty($params['field']) && strpos($params['order_no'], 'VHA') === false) {
            $source = explode(',', $params['field']);
            foreach ($source as $k => $v) {
                if ($v == 'is_supplier_delivery') {
                    unset($source[$k]);
                    $source[] = 'warehouse_code';
                }
            }
            //删除重复的值
            $source = array_merge(array_unique($source));
        }

        $es   = new ElasticSearchService();
        $arr  = array(
            'index'  => ['orders'],
            'match'  => $where,
            'source' => $source,
            'limit'  => 1,
        );
        $data = $es->getDocumentList($arr);
        if (!isset($data['data'][0])) $this->throwError('未获取到订单详情');
        $orderDetail = $data['data'][0];
        if (isset($orderDetail['banner_img'])) $orderDetail['banner_img'] = imagePrefix($orderDetail['banner_img']);
        if (isset($orderDetail['payment_subject'])) {
            $orderDetail['payment_subject_name'] = isset(config('config')['payment_subject'][intval($orderDetail['payment_subject'])]['label']) ? config('config')['payment_subject'][intval($orderDetail['payment_subject'])]['label'] : '';
        }
        if (isset($orderDetail['order_type']) && isset($orderDetail['express_type']) && isset($orderDetail['express_name']) && $orderDetail['order_type'] == 9) {
            //快递名称处理
            $express_name = '';
            switch ($orderDetail['express_type']) {
                case 2:
                    $express_name = '顺丰快递';
                    break;
                case 4:
                case 5:
                    $express_name = '京东快递';
                    break;
                case 10:
                    $express_name = '京东物流';
                    break;
                case 65:
                case 100:
                    $express_name = isset($orderDetail['express_name']) ? $orderDetail['express_name'] : '其他快递';
                    break;
                default:
                    $express_name = isset($orderDetail['express_name']) ? $orderDetail['express_name'] : '其他快递';
            }
            $orderDetail['express_name'] = $express_name;
        }

        // 查询是否代发
        if (!empty($params['field']) && strpos_str($params['field'], 'is_supplier_delivery') !== false) {
            $orderDetail['is_supplier_delivery'] = 0;
            if (!empty($orderDetail['warehouse_code']) && in_array(intval($orderDetail['warehouse_code']), GetDfWarehouseCode('virtual_id'))) {
                $orderDetail['is_supplier_delivery'] = 1;
            }
        }

        $orderDetail['combination'] = [];
        if (!empty($orderDetail['main_order_no'])) {
            $lock_order                 = CrossLockOrder::where([
                'main_order_no' => $orderDetail['main_order_no'],
                'status'        => 1,
            ])->find();
            $orderDetail['lock_status'] = $lock_order['status'] ?? 0;
            $orderDetail['lock_remark'] = $lock_order['remark'] ?? '';

            $cross_split = Db::name('cross_split')
                ->where('origin_main_order_no', 'in', Db::name('cross_split')
                    ->where('main_order_no', '=', $orderDetail['main_order_no'])
                    ->column('origin_main_order_no'))
                ->column('origin_main_order_no,main_order_no');

            $cssbs                      = Db::name('cross_order')->alias('o')
                ->field('om.main_order_no,o.sub_order_no')
                ->leftJoin('order_main om', 'om.id=o.main_order_id')
                ->where(['om.main_order_no' => array_column($cross_split, 'main_order_no')])
                ->select()->toArray();
            $orderDetail['combination'] = array_column($cssbs, 'sub_order_no');
        }

        return $orderDetail;
    }

    /**
     * Description:队列处理超时订单
     * Author: zrc
     * Date: 2021/8/14
     * Time: 17:17
     * @param $requestparams
     */
    public function timeOutOrderDeal($requestparams)
    {
        $params = json_decode($requestparams, true);
        if (!isset($params['main_order_no'])) $this->throwError('未获取到订单号');
        $main_order_no = $params['main_order_no'];
        try {
            if (in_array($main_order_no, [
                'VHM240514008119420868',
                'VHS240514008119427227',
            ])) {
                Log::write("特殊订单号不取消订单: {$main_order_no}");
                return true;
            }
        } catch (\Exception $e) {
        }

        if (strpos($main_order_no, 'VHP') !== false) {//酒会
            $winepartyDeal = httpPostString(env('ITEM.WINEPARTY_URL') . '/wineparty/v3/order/update', json_encode(['main_order_no' => $main_order_no, 'main_order_status' => 2, 'operator' => 0]));
            if (!isset($winepartyDeal['error_code']) || $winepartyDeal['error_code'] != 0) {
                Log::error($main_order_no . '：酒会超时订单处理失败！' . $winepartyDeal['error_msg']);
            }
        } else if (strpos($main_order_no, 'VHC') !== false) {//课程
            $orderInfo = Db::name('wine_academy_order')->field('main_order_status')->where(['main_order_no' => $main_order_no])->find();
            if (empty($orderInfo)) {
                $this->throwError('未获取到订单信息');
            }
            if ($orderInfo['main_order_status'] == 0) {
                Db::name('wine_academy_order')->where(['main_order_no' => $main_order_no])->update(['main_order_status' => 4]);
            }
        } else {//其他订单
            $orderInfo = Db::name('order_main')->field('id,main_order_status,order_type,payment_method,payment_subject')->where(['main_order_no' => $main_order_no])->find();
            $order_log = Db::name('order_deal_log')->where(['main_order_no' => $main_order_no])->find();
            if (empty($orderInfo) || empty($order_log)) {
                $this->throwError($main_order_no . ':未获取到订单信息');
            }
            if ($orderInfo['main_order_status'] == 0) {
                /**查询当前订单是否创建订单待异步回调，是-主动拦截取消订单 start**/
                //过滤未发起支付请求订单
                if ($orderInfo['payment_method'] >= 0) {
                    switch ($orderInfo['payment_subject']) {
                        case 1:
                        case 2://银联
                            $queryData = array(
                                'main_order_no'  => $orderInfo['payment_method'] . $main_order_no,
                                'payment_method' => $orderInfo['payment_method'],
                                'subject'        => $orderInfo['payment_subject'],
                                'is_cross'       => $orderInfo['order_type'] == 2 ? 1 : 0,
                            );
                            $umsInfo   = httpPostString(env('ITEM.PAYMENT_URL') . '/payment/v3/ums/query', json_encode($queryData));
                            if (isset($umsInfo['error_code']) && $umsInfo['error_code'] == 0) {
                                $umsInfo['data']['status'] = $umsInfo['data']['status'] ?? '';
                                if (isset($umsInfo['data']['billPayment']['status'])) $umsInfo['data']['status'] = $umsInfo['data']['billPayment']['status'];
//                                if ($umsInfo['data']['status'] == 'TRADE_SUCCESS' && isset($order_log['notify_param'])) {
                                if ($umsInfo['data']['status'] == 'TRADE_SUCCESS') {
                                    //支付成功重推订单异步处理队列
//                                    $notifyService = new NotifyService();
//                                    $notifyService->notify($order_log['notify_param']);
                                    return true;
                                }
                            }
                            $cancelOrderData = array(
                                'main_order_no'  => $orderInfo['payment_method'] . $main_order_no,
                                'payment_method' => $orderInfo['payment_method'],
                                'subject'        => $orderInfo['payment_subject'],
                                'is_cross'       => $orderInfo['order_type'] == 2 ? 1 : 0,
                            );
                            $umsCancel       = httpPostString(env('ITEM.PAYMENT_URL') . '/payment/v3/ums/cancel', json_encode($cancelOrderData));
                            if (!isset($umsCancel['error_code']) || $umsCancel['error_code'] != 0) {
                                Log::error($main_order_no . '：主动取消银联订单失败！' . isset($umsCancel['error_msg']) ? $umsCancel['error_msg'] : '支付模块返回异常');
                            }
                            break;
                        case 4://微信支付宝
                            $method = 'alipay';
                            if (in_array($orderInfo['payment_method'], [3, 4, 5, 7, 8, 9])) $method = 'wechat';
                            $queryData     = array(
                                'main_order_no'  => $main_order_no,
                                'method'         => $method,
                                'payment_method' => $orderInfo['payment_method']
                            );
                            $weiAndAliInfo = httpPostString(env('ITEM.PAYMENT_URL') . '/payment/v3/weAndAli/query', json_encode($queryData));
                            if (isset($weiAndAliInfo['error_code']) && $weiAndAliInfo['error_code'] == 0) {
                                if ((($method == 'alipay' && $weiAndAliInfo['data']['trade_status'] == 'TRADE_SUCCESS') || ($method == 'wechat' && $weiAndAliInfo['data']['trade_state'] == 'SUCCESS')) && isset($order_log['notify_param'])) {
                                    //支付成功重推订单异步处理队列
                                    $notifyService = new NotifyService();
                                    $notifyService->notify($order_log['notify_param']);
                                    return true;
                                }
                            }
                            $cancelOrderData = array(
                                'main_order_no'  => $main_order_no,
                                'method'         => $method,
                                'payment_method' => $orderInfo['payment_method']
                            );
                            $weiAndAliCancel = httpPostString(env('ITEM.PAYMENT_URL') . '/payment/v3/weAndAli/cancel', json_encode($cancelOrderData));
                            if (!isset($weiAndAliCancel['error_code']) || $weiAndAliCancel['error_code'] != 0) {
                                Log::error($main_order_no . '：主动取消订单失败！' . isset($weiAndAliCancel['error_msg']) ? $weiAndAliCancel['error_msg'] : '支付模块返回异常');
                            }
                            break;
                    }
                }
                /**查询当前订单是否创建订单待异步回调，是-主动拦截取消订单 end**/
                Db::startTrans();
                try {
                    $main = Db::name('order_main')->where(['main_order_no' => $main_order_no])->lock(true)->find();
                    Db::name('order_main')->where(['main_order_no' => $main_order_no])->update([
//                        'refund_recharge_balance' => Db::raw('recharge_balance'),
//                        'refund_bonus_balance' => Db::raw('bonus_balance'),
                        'main_order_status' => 4,
                        'update_time' => time()
                    ]);
                    //代付记录处理
                    if ($orderInfo['order_type'] == 2) {
                        Db::name('cross_pay_on_behalf')->where(['main_order_no' => $main_order_no])->update(['status' => 2, 'update_time' => time()]);
                    }
                    $order_type     = config('config')['order_type'];//订单频道获取
                    $order_type_arr = explode(',', $orderInfo['order_type']);
                    foreach ($order_type_arr as &$val) {
                        Db::name($order_type[intval($val)]['table'])->where(['main_order_id' => $orderInfo['id']])->update([
                            'refund_recharge_balance' => Db::raw('recharge_balance'),
                            'refund_bonus_balance' => Db::raw('bonus_balance'),
                            'sub_order_status' => 4,
                            'update_time' => time()
                        ]);
                        //关联订单解冻，用户自动升级冷链取消订单需要
                        $related_order_no = Db::name($order_type[intval($val)]['table'])->where(['main_order_id' => $orderInfo['id']])->value('related_order_no');
                        if (!empty($related_order_no)) {
                            $related_order_info = esGetOne($related_order_no, 'vinehoo.orders');
                            if (isset($related_order_info['freeze_status']) && $related_order_info['freeze_status'] == 1) {
                                $freezeData        = array(
                                    'sub_order_no' => $related_order_no,
                                    'order_type'   => $related_order_info['order_type'],
                                    'type'         => 2,
                                );
                                $afterSalesService = new AfterSalesService();
                                try {
                                    $afterSalesService->freezeOrder($freezeData);
                                } catch (\Exception $e) {
                                }
                            }
                        }
                    }
                    //库存退还
                    $timeout_deal_status = json_decode($order_log['timeout_deal_status'], true);
                    if ($timeout_deal_status['stock'] == 0) {
                        $stock_param = json_decode($order_log['stock_param'], true);
                        if ($stock_param['groupid'] > 0 || ($stock_param['group_status'] == 0 && $stock_param['groupid'] == 0)) {
                            $stockVerify = curlRequest(env('ITEM.VINEHOO_INVENTORY_INOUT_URL') . '/inventory_service/v3/inventory/inc', json_encode($stock_param, true));
                            if ($stockVerify['error_code'] != 0) $this->throwError($stockVerify['error_msg']);
                        }
                        $timeout_deal_status['stock'] = 1;
                    }
                    //优惠券退还
                    if ($timeout_deal_status['coupon'] == 0) {
                        $create_param = json_decode($order_log['create_param'], true);
                        $couponArr    = [];
                        if ($create_param['coupon_id'] != 0) {
                            $couponArr[] = ['id' => $create_param['coupon_id']];
                        }
                        if ($create_param['express_coupon_id'] != 0) {
                            $couponArr[] = ['id' => $create_param['express_coupon_id']];
                        }
                        if (count($couponArr) > 0) {
                            $couponPushData = array(
                                'type'    => 2,
                                'coupons' => $couponArr,
                            );
                            $header         = array(
                                "content-type: application/json",
                                "vinehoo-client: orders",
                                "vinehoo-uid: " . $create_param['uid'],
                            );
                            $pushCoupon     = httpPostString(env('ITEM.COUPON_URL') . '/coupon/v3/couponissue/change', json_encode($couponPushData), $header);
                            if ($pushCoupon['error_code'] != 0) $this->throwError('返还优惠券失败！');
                        }
                        $timeout_deal_status['coupon'] = 1;
                    }
                    //拼团订单未支付处理（只有闪购才有拼团longfei）
                    $create_param = json_decode($order_log['create_param'], true);
                    if (isset($create_param['special_type'])) {
                        if ($create_param['special_type'] == 1) {
                            $table = 'flash_order';
                            if ($create_param['submit_type'] == 9) $table = 'merchant_second_order';
                            Db::name($table)->where(['main_order_id' => $orderInfo['id']])->update(['group_status' => 3]);
                        }
                    }
                    //修改日志
                    Db::name('order_deal_log')->where(['main_order_no' => $main_order_no])->update(['timeout_deal_status' => json_encode($timeout_deal_status)]);

                    if (($main['recharge_balance'] > 0) || ($main['bonus_balance'] > 0)) {
                        // Record refund log
                        $balance_logs[] = [
                            'uid' => $main['uid'],
                            'main_order_no' => $main['main_order_no'],
                            'sub_order_no' => '',
                            'recharge_balance' => $main['recharge_balance'],
                            'bonus_balance' => $main['bonus_balance'],
                            'created_time' => time(),
                            'remark' => '超时取消订单退还余额 ',
                            'updated_time' => time(),
                            'type' => 1,
                            'status' => 0,
                            'request' => json_encode([
                                'uid' => intval($main['uid']),
                                'type' => 1, // 增加
                                'related_no' => $main['main_order_no'],
                                'related_type' => 2, // 商品订单
                                'operation_type' => 5, // 商品购买
                                'recharge_balance' => floatval($main['recharge_balance']),
                                'bonus_balance' => floatval($main['bonus_balance']),
                                'change_time' => time(),
                                'change_name' => '系统',
                                'unique_code' => "REF" . $main['main_order_no']
                            ]),
                        ];
                        Db::name('balance_pay')->insertAll($balance_logs);
                    }

                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    //日志记录
                    Log::error($main_order_no . '：超时订单处理失败！' . $e->getMessage());
                    $this->throwError($main_order_no . '：超时订单处理失败！' . $e->getMessage());
                }
            }
        }
        return true;
    }

    /**
     * Description:拼团超时订单队列处理
     * Author: zrc
     * Date: 2022/5/13
     * Time: 14:28
     * @param $requestparams
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function groupOrderTimeOutDeal($requestparams)
    {
        Log::info('拼团超时订单处理参数:{info}', ['info' => $requestparams]);
        $params = json_decode($requestparams, true);
        if (!isset($params['head_order_no'])) $this->throwError('未获取到团长订单号', ErrorCode::PARAM_ERROR);
        if (!isset($params['group_id'])) $this->throwError('未获取到拼团ID', ErrorCode::PARAM_ERROR);
        $groupInfo = Db::name('order_group')->where(['head_order_no' => $params['head_order_no'], 'id' => $params['group_id']])->find();
        if (empty($groupInfo)) $this->throwError('未获取到拼团信息');
        $headOrderInfo = Db::name('order_main')->field('id,main_order_status,order_type')->where(['main_order_no' => $params['head_order_no']])->find();
        if (empty($headOrderInfo)) $this->throwError('未获取到团长订单信息');
        $order_type = config('config')['order_type'];//订单频道获取
        Db::startTrans();
        try {
            //未支付拼团订单，只变更拼团状态
            if ($headOrderInfo['main_order_status'] == 0 || $headOrderInfo['main_order_status'] == 4) {
                Db::name('order_group')->where(['head_order_no' => $params['head_order_no'], 'id' => $params['group_id']])->update(['group_status' => 3]);
                Db::name($order_type[intval($headOrderInfo['order_type'])]['table'])->where(['main_order_id' => $headOrderInfo['id']])->update(['sub_order_status' => 4, 'group_status' => 3, 'update_time' => time()]);
            }
            //已支付拼团订单退款处理
            if ($headOrderInfo['main_order_status'] == 1 && $groupInfo['group_status'] == 1) {
                Db::name('order_group')->where(['head_order_no' => $params['head_order_no'], 'id' => $params['group_id']])->update(['group_status' => 3]);
                $main_order_no_arr = [$groupInfo['head_order_no']];
                if (!empty($groupInfo['member_order_no'])) {
                    $main_order_no_arr = explode(',', $groupInfo['head_order_no'] . ',' . $groupInfo['member_order_no']);
                }
                $mainInfo = Db::name('order_main')->field('id,uid,payment_method,recharge_balance,cash_amount,bonus_balance,payment_amount,main_order_no,order_type,payment_subject')->where([['main_order_no', 'in', $main_order_no_arr]])->select()->toArray();
                $balance_logs = [];
                foreach ($mainInfo as &$val) {
                    $subOrder = Db::name($order_type[intval($headOrderInfo['order_type'])]['table'])->field('recharge_balance,bonus_balance,uid,refund_recharge_balance,refund_bonus_balance,package_id,sub_order_no,sub_order_status,group_status')->where(['main_order_id' => $val['id']])->find();
                    if (empty($subOrder)) $this->throwError('未获取到拼团子订单信息');
                    if ($subOrder['sub_order_status'] == 1 && $subOrder['group_status'] == 1) {
                        if (($subOrder['recharge_balance'] > 0) || ($subOrder['bonus_balance'] > 0)) {
                            // Record refund log
                            $balance_logs[] = [
                                'uid' => $subOrder['uid'],
                                'main_order_no' => $val['main_order_no'],
                                'sub_order_no' => $subOrder['sub_order_no'],
                                'recharge_balance' => floatval(bcsub($subOrder['recharge_balance'], $subOrder['refund_recharge_balance'], 2)),
                                'bonus_balance' => floatval(bcsub($subOrder['bonus_balance'], $subOrder['refund_bonus_balance'], 2)),
                                'created_time' => time(),
                                'remark' => '拼团失败退还余额 ',
                                'updated_time' => time(),
                                'type' => 1,
                                'status' => 0,
                                'request' => json_encode([
                                    'uid' => intval($subOrder['uid']),
                                    'type' => 1, // 增加
                                    'related_no' => $val['main_order_no'],
                                    'related_type' => 2, // 商品订单
                                    'operation_type' => 5, // 商品购买
                                    'recharge_balance' => floatval(bcsub($subOrder['recharge_balance'], $subOrder['refund_recharge_balance'], 2)),
                                    'bonus_balance' => floatval(bcsub($subOrder['bonus_balance'], $subOrder['refund_bonus_balance'], 2)),
                                    'change_time' => time(),
                                    'change_name' => '系统',
                                    'unique_code' => "REF" . $subOrder['sub_order_no'],
                                ]),
                            ];
                        }

                        $editOrder = Db::name($order_type[intval($headOrderInfo['order_type'])]['table'])->where(['main_order_id' => $val['id']])->update(['sub_order_status' => 4, 'group_status' => 3, 'update_time' => time(), 'refund_status' => 1]);
                        if (empty($editOrder)) $this->throwError('修改拼团订单信息失败');
                        //生成退款订单
                        $refund_order_no = creatOrderNo(env('ORDERS.REFUND'), $val['uid']);
                        $insertData      = array(
                            'refund_order_no' => $refund_order_no,
                            'main_order_no'   => $val['main_order_no'],
                            'refund_status'   => 0,
                            'sub_order_no'    => $subOrder['sub_order_no'],
                            'payment_method'  => $val['payment_method'],
                            'payment_subject' => $val['payment_subject'],
                            'refund_amount'   => $val['cash_amount'],
                            'created_time'    => time(),
                        );
                        $addRefund       = Db::name('refund_order')->insert($insertData);
                        if (empty($addRefund)) $this->throwError('生成退款订单失败');
                        //发起银联退款
                        $pushData    = array(
                            'main_order_no'   => $val['payment_method'] . $val['main_order_no'],//支付方式+主订单号为银联交易订单号
                            'payment_method'  => $val['payment_method'],
                            'refund_amount'   => $val['cash_amount'],
                            'refund_order_no' => $refund_order_no,
                            'subject'         => $val['payment_subject'],
                            'is_cross'        => $val['order_type'] == 2 ? 1 : 0,
                            'order_type'      => $val['order_type'],//子订单单个退传本身订单类型
                            'sub_order_no'    => $subOrder['sub_order_no']
                        );
                        $orderRefund = $this->httpPost(env('ITEM.PAYMENT_URL') . '/payment/v3/ums/refund', $pushData);
//                        if (!isset($orderRefund['error_code']) || $orderRefund['error_code'] != 0) {
//                            //修改退款订单状态
//                            Db::name('refund_order')->where(['refund_order_no' => $refund_order_no, 'main_order_no' => $val['main_order_no'], 'sub_order_no' => $subOrder['sub_order_no']])->update(['refund_status' => 2, 'update_time' => time()]);
//                            $this->throwError('发起退款失败：' . $orderRefund['error_msg']);
//                        }
                        //修改订单状态
                        $editOrder = Db::name($order_type[intval($headOrderInfo['order_type'])]['table'])->where(['sub_order_no' => $subOrder['sub_order_no']])->update(['refund_status' => 2, 'refund_start_time' => time(), 'refund_end_time' => time()]);
                        if (empty($editOrder)) $this->throwError('修改订单状态失败');
                        //修改退款订单状态
                        $updateRefund = Db::name('refund_order')->where(['refund_order_no' => $refund_order_no, 'main_order_no' => $val['main_order_no'], 'sub_order_no' => $subOrder['sub_order_no']])->update(['refund_status' => 1, 'update_time' => time()]);
                        if (empty($updateRefund)) $this->throwError('修改退款订单失败');
                        //预防手动或其他情况已推送发货仓，执行一次撤单
                        $this->httpPost(env('ITEM.PUSH_ORDERS_URL') . '/pushorders/v3/cancel/order', ['sub_order_no' => $subOrder['sub_order_no']]);
                    }
                }
                Db::name('balance_pay')->insertAll($balance_logs);
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            Log::error('拼团超时订单处理失败：' . $e->getMessage());
            $this->throwError($params['group_id'] . '拼团超时订单处理失败:' . $e->getMessage());
        }

    }

    /**
     * Description:可开票订单列表
     * Author: zrc
     * Date: 2021/8/17
     * Time: 16:22
     * @param $requstparams
     * type 类型：0-普通订单 1-课程订单 2-酒会订单
     */
    public function invoiceOrderList($requestparams)
    {
        $page   = !empty($requestparams['page']) ? $requestparams['page'] : 1;
        $limit  = !empty($requestparams['limit']) ? $requestparams['limit'] : 10;
        $params = $requestparams;
        if ($params['type'] == 0) {//普通订单
            $match_phrase = [];
            $where[]      = ['sub_order_status' => 3];
            $where[]      = ['is_virtual' => 0];
            $where[]      = ['is_gift' => 0];
            $where[]      = ['refund_status' => 0];
            $range[]      = ['payment_amount' => ['gt' => 0]];
            if (!empty($params['uid'])) {
                $where[] = ['uid' => $params['uid']];
            }
            if (!empty($params['sub_order_no'])) {
                $where[] = ['sub_order_no.keyword' => $params['sub_order_no']];
            }
            if (isset($params['order_type']) && is_numeric($params['order_type'])) {
                $where[] = ['order_type' => $params['order_type']];
            }
            if (!empty($params['stime'])) {
                $range[] = ['created_time' => ['gte' => $params['stime']]];
            }
            if (!empty($params['etime'])) {
                $range[] = ['created_time' => ['lte' => $params['etime']]];
            }
            if (!empty($params['period'])) {
                $where[] = ['period' => $params['period']];
            }
            if (!empty($params['nickname'])) {
                $match_phrase[] = ['nickname' => $params['nickname']];
            }
            if (!empty($params['consignee'])) {
                //收件人加密查询
                $encrypt             = cryptionDeal(1, [$params['consignee']], '15736175219', '宗仁川');
                $params['consignee'] = isset($encrypt[$params['consignee']]) ? $encrypt[$params['consignee']] : '';
                $where[]             = ['consignee' => $params['consignee']];
            }
            if (!empty($params['consignee_phone'])) {
                //收件人加密查询
                $encrypt                   = cryptionDeal(1, [$params['consignee_phone']], '15736175219', '宗仁川');
                $params['consignee_phone'] = isset($encrypt[$params['consignee_phone']]) ? $encrypt[$params['consignee_phone']] : '';
                $where[]                   = ['consignee_phone' => $params['consignee_phone']];
            }
            if(!empty($params['payment_amount_sort'])){
                $order = [['payment_amount' => $params['payment_amount_sort']]];
            }else{
                $order = [['created_time' => 'desc']];
            }
            $es    = new ElasticSearchService();

            if (!empty($params['payee_merchant_id']) || !empty($params['product_main_category'])) {
                $arr        = array(
                    'index'        => ['orders'],
                    'match'        => $where,
                    'match_phrase' => $match_phrase,
                    'range'        => $range,
                    'terms'        => [['order_type' => [0, 1, 3, 9]], ['invoice_progress' => [0, 3]]],
                    'source'       => ['period'],
                    'sort'         => $order
                );
                $data       = $es->getDocumentList($arr);
                $p_es_where = [['_id', 'in', array_values(array_unique(array_column($data['data'], 'period')))]];
                if (!empty($params['product_main_category'])) {
                    $p_es_where[] = ['product_main_category', 'in', explode(',', $params['product_main_category'])];
                }
                if (!empty($params['payee_merchant_id'])) {
                    $p_es_where[] = ['payee_merchant_id', 'in', explode(',', $params['payee_merchant_id'])];
                }

                $where_periods = Es::name(Es::PERIODS)->where($p_es_where)->field('id')->select()->toArray();
                $matchIn       = ['period' => array_column($where_periods, 'id')];

            }
            $terms = [['order_type' => [0, 1, 3, 9]], ['invoice_progress' => [0, 3]]];
            if (!empty($matchIn)) {
                $terms[] = $matchIn;
            }

            $arr   = array(
                'index'        => ['orders'],
                'match'        => $where,
                'match_phrase' => $match_phrase,
                'range'        => $range,
                'terms'        => $terms,
                'source'       => ['id', 'uid', 'nickname', 'sub_order_no', 'order_type', 'order_qty', 'period', 'package_id', 'payment_amount', 'title', 'package_name', 'banner_img', 'refund_status', 'invoice_progress', 'created_time', 'consignee', 'consignee_phone', 'consignee_encrypt', 'consignee_phone_encrypt', 'created_time'],
                'page'         => $page,
                'limit'        => $limit,
                'sort'         => $order
            );
            $data  = $es->getDocumentList($arr);
            if (count($data['data']) > 0) {
                $packageWhere = [];
                $should       = [];
                $periodWhere  = [];
                $periodShould = [];
                foreach ($data['data'] as &$val) {
                    $should[]       = ['match_phrase' => ['id' => $val['package_id']]];
                    $periodShould[] = ['match_phrase' => ['id' => $val['period']]];
                }
                $packageList = [];
                if (!empty($should)) {
                    $packageWhere [] = ['bool' => ['should' => $should]];
                    $esData          = esGetList('vinehoo.periods_set', $packageWhere);
                    if (isset($esData['hits']['hits'])) {
                        foreach ($esData['hits']['hits'] as $temp) {
                            $packageInfo                     = $temp['_source'];
                            $packageList[$packageInfo['id']] = $packageInfo['associated_products'];
                        }
                    }
                }
                $periodList = [];
                if (!empty($periodShould)) {
                    $periodWhere [] = ['bool' => ['should' => $periodShould]];
                    $pesData        = esGetList('vinehoo.periods', $periodWhere);
                    if (isset($pesData['hits']['hits'])) {
                        foreach ($pesData['hits']['hits'] as $temp) {
                            $periodInfo                    = $temp['_source'];
                            $periodList[$periodInfo['id']] = isset($periodInfo['payee_merchant_name']) ? $periodInfo['payee_merchant_name'] : '';
                        }
                    }
                }
                foreach ($data['data'] as &$v) {
                    $v['banner_img'] = imagePrefix($v['banner_img']);
                    $v['is_invoice'] = 0;
                    if ($v['invoice_progress'] != 0) $v['is_invoice'] = 1;
                    if (isset($packageList[$v['package_id']])) {
                        $v['associated_products'] = $packageList[$v['package_id']];
                    }
                    if (isset($periodList[$v['period']])) {
                        $v['payee_merchant_name'] = $periodList[$v['period']];
                        //2022-9-30号之前默认云酒公司
                        if (strtotime($v['created_time']) <= '1664467200') {
                            $v['payee_merchant_name'] = '重庆云酒佰酿电子商务有限公司';
                        }
                    }
                }
            }
        } else if ($params['type'] == 1) {//课程订单
            $offset = ($page - 1) * $limit;
            $table  = Db::name('wine_academy_order');
            if (!empty($params['uid'])) {
                $table->where(['uid' => $params['uid']]);
            }
            $totalNums     = $table->count();
            $academy_order = $table->field('id,uid,main_order_no,payment_amount,refund_status,invoice_progress,created_time')->limit($offset, $limit)->order('id desc')->select()->toArray();
            $data          = array(
                'total' => ['value' => $totalNums],
                'data'  => $academy_order
            );
        } else if ($params['type'] == 2) {//酒会订单
//            $where[] = ['main_order_status' => 1];
//            $where[] = ['refund_status' => 0];
//            $range[] = ['payment_amount' => ['gt' => 0]];
//            if (!empty($params['uid'])) {
//                $where[] = ['uid' => $params['uid']];
//            }
//            $order = [['created_time' => 'desc']];
//            $es    = new ElasticSearchService();
//            $arr   = array(
//                'index'  => ['orders_wineparty'],
//                'match'  => $where,
//                'range'  => $range,
//                'source' => ['id', 'uid', 'main_order_no', 'payment_amount', 'refund_status', 'invoice_progress', 'created_time'],
//                'page'   => $page,
//                'limit'  => $limit,
//                'sort'   => $order
//            );
//            $data  = $es->getDocumentList($arr);
            $data['total']['value'] = 0;
            $data['data']           = [];
        }
        $totalNum        = $data['total']['value'];
        $result['list']  = $data['data'];
        $result['total'] = $totalNum;
        return $result;
    }

    /**
     * Description:个人中心可开票订单列表
     * Author: zrc
     * Date: 2022/1/26
     * Time: 10:38
     * @param $requstparams
     * @return mixed
     */
    public function personalInvoiceOrderList($requestparams)
    {
        $page    = !empty($requestparams['page']) ? $requestparams['page'] : 1;
        $limit   = !empty($requestparams['limit']) ? $requestparams['limit'] : 10;
        $params  = $requestparams;
        $time    = date('Y-m-d H:i:s', time() - 365 * 86400);
        $where[] = ['uid' => $params['uid']];
        $where[] = ['sub_order_status' => 3];
        $where[] = ['is_virtual' => 0];
        $where[] = ['is_gift' => 0];
        $where[] = ['refund_status' => 0];
        $range[] = ['cash_amount' => ['gte' => 1]];
        $range[] = ['created_time' => ['gte' => $time]];
        if (isset($params['order_type']) && is_numeric($params['order_type'])) {
            $where[] = ['order_type' => $params['order_type']];
        }
        if (!empty($params['stime']) && $params['stime'] > $time) {
            $range[] = ['created_time' => ['gte' => $params['stime']]];
        }
        if (!empty($params['etime'])) {
            $range[] = ['created_time' => ['lte' => $params['etime']]];
        }
        if (!empty($params['smoney'])) {
            $range[] = ['cash_amount' => ['gte' => $params['smoney']]];
        }
        if (!empty($params['emoney'])) {
            $range[] = ['cash_amount' => ['lte' => $params['emoney']]];
        }
        $order        = [['created_time' => 'desc']];
        $es           = new ElasticSearchService();
        $arr          = array(
            'index'  => ['orders'],
            'match'  => $where,
            'range'  => $range,
            'terms'  => [['order_type' => [0, 1, 3, 9]], ['invoice_progress' => [0, 3]]],
            'source' => ['id', 'uid', 'sub_order_no', 'order_type', 'order_qty', 'period', 'package_id', 'cash_amount','payment_amount', 'title', 'package_name', 'banner_img', 'refund_status', 'invoice_progress', 'created_time', 'sub_order_status'],
            'page'   => $page,
            'limit'  => $limit,
            'sort'   => $order
        );
        $data         = $es->getDocumentList($arr);
        $packageWhere = [];
        $should       = [];
        foreach ($data['data'] as &$val) {
            $should[] = ['match_phrase' => ['id' => $val['package_id']]];
        }
        $packageList = [];
        if (!empty($should)) {
            $packageWhere [] = ['bool' => ['should' => $should]];
            $esData          = esGetList('vinehoo.periods_set', $packageWhere);
            if (isset($esData['hits']['hits'])) {
                foreach ($esData['hits']['hits'] as $temp) {
                    $packageInfo                     = $temp['_source'];
                    $packageList[$packageInfo['id']] = $packageInfo['associated_products'];
                }
            }
        }

        foreach ($data['data'] as &$v) {
            $v['banner_img'] = imagePrefix($v['banner_img']);
            $v['cash_amount'] = $v['cash_amount'] ?? ($v['payment_amount'] ?? 0);
            $v['payment_amount'] = $v['cash_amount'] ?? ($v['payment_amount'] ?? 0);
            if (isset($packageList[$v['package_id']])) {
                $v['associated_products'] = $packageList[$v['package_id']];
            }
        }

        $totalNum        = $data['total']['value'];
        $result['list']  = $data['data'];
        $result['total'] = $totalNum;
        return $result;
    }

    /**
     * Description:创建订单备注
     * Author: zrc
     * Date: 2022/5/7
     * Time: 16:19
     * @param $requstparams
     * @return int|string
     * @throws \think\db\exception\DbException
     */
    public function createRemarks($requstparams)
    {
        $params     = $requstparams;
        $order_type = config('config')['order_type'];//订单频道获取
        Db::name($order_type[intval($params['order_type'])]['table'])->where(['sub_order_no' => $params['sub_order_no']])->update(['remarks' => $params['content']]);
        $result = Db::name('order_remarks')->insert(['sub_order_no' => $params['sub_order_no'], 'admin_id' => $params['admin_id'], 'remarks' => $params['content'], 'created_time' => time()]);
        return $result;
    }

    /**
     * Description:获取订单备注
     * Author: zrc
     * Date: 2021/8/19
     * Time: 18:30
     * @param $requstparams
     */
    public function remarksList($requstparams)
    {
        $params = $requstparams;
        $result = Db::name('order_remarks')->where(['sub_order_no' => $params['sub_order_no']])->order('id desc')->select()->toArray();
        if (count($result) > 0) {
            $admin_id = array_unique(array_column($result, 'admin_id'));
            foreach ($admin_id as $key => $val) {
                if ($val == 0) unset($admin_id[$key]);
            }
            $adminInfo = $this->httpGet(env('ITEM.AUTHORITY_URL') . '/authority/v3/admin/info', ['admin_id' => implode(',', $admin_id), 'field' => 'realname']);
            foreach ($result as &$val) {
                $val['created_time'] = date('Y-m-d H:i:s', $val['created_time']);
                if ($val['admin_id'] == 0) $val['admin_id'] = '系统';
                if ($val['admin_id'] == -1) $val['admin_id'] = '前端用户';
                if (isset($adminInfo['data'][$val['admin_id']])) $val['admin_id'] = $adminInfo['data'][$val['admin_id']];
            }
        }
        return ['list' => $result];
    }

    /**
     * Description:获取订单路由列表
     * Author: zrc
     * Date: 2021/11/11
     * Time: 9:49
     * @param $requstparams
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function routingList($requstparams)
    {
        $params = $requstparams;
        $result = Db::name('order_routing')->where(['sub_order_no' => $params['sub_order_no']])->order('id desc')->select()->toArray();
        foreach ($result as &$val) {
            $val['created_time'] = date('Y-m-d H:i:s', $val['created_time']);
        }
        return $result;
    }

    /**
     * Description:公共修改订单信息
     * Author: zrc
     * Date: 2021/8/24
     * Time: 13:41
     * @param $params
     */
    public function updateOrder($requstparams)
    {
        $params = $requstparams;
        if ($params['order_type'] == 5) {//酒会
            $updateData = array(
                'main_order_no' => $params['order_no'],
                'operator'      => $params['operator']
            );
            if (isset($params['invoice_progress']) && is_numeric($params['invoice_progress'])) {
                $updateData['invoice_progress'] = $params['invoice_progress'];
            }
            if (!empty($params['invoice_id'])) {
                $updateData['invoice_id'] = $params['invoice_id'];
            }
            $result = httpPostString(env('ITEM.WINEPARTY_URL') . '/wineparty/v3/order/update', json_encode($updateData));
            if ($result['error_code'] != 0) $this->throwError($result['error_msg']);
            return true;
        } else if ($params['order_type'] == 6) {//课程
            $where = array('main_order_no' => $params['order_no']);
            if (isset($params['invoice_progress']) && is_numeric($params['invoice_progress'])) $updateData['invoice_progress'] = $params['invoice_progress'];
            if (!empty($params['invoice_id'])) $updateData['invoice_id'] = $params['invoice_id'];
            $courseModel = new CourseModel();
            $result      = $courseModel->updateOrder($updateData, $where);
            return $result;
        }
        $order_type = config('config')['order_type'];//订单频道获取
        $orderInfo  = Db::name($order_type[intval($params['order_type'])]['table'])
            ->alias('so')
            ->field('so.*,om.main_order_no,om.payment_method,om.province_id,om.city_id,om.district_id,om.address,om.consignee,om.consignee_phone')
            ->leftJoin('order_main om', 'om.id=so.main_order_id')
            ->where(['so.sub_order_no' => $params['order_no']])
            ->find();
        if (empty($orderInfo)) $this->throwError('未获取到订单信息');
        $updateData        = [];
        $updateMainData    = [];
        $updateReceiveData = [];
        $remarks           = [];
        if (isset($params['sub_order_status']) && is_numeric($params['sub_order_status'])) {
            $updateData['sub_order_status'] = $params['sub_order_status'];
            if ($params['sub_order_status'] == 2) $updateData['delivery_time'] = time();
            if ($params['sub_order_status'] == 3) $updateData['goods_receipt_time'] = time();
        }
        if (isset($params['invoice_progress']) && is_numeric($params['invoice_progress'])) $updateData['invoice_progress'] = $params['invoice_progress'];
        if (!empty($params['invoice_id'])) $updateData['invoice_id'] = $params['invoice_id'];
        if (isset($params['refund_status']) && is_numeric($params['refund_status'])) {
            if ($params['refund_status'] == 1) $updateData['refund_start_time'] = time();
            if ($params['refund_status'] == 2) {
                $updateData['refund_end_time'] = time();
                //期数已购标识移除
                $additionalService = new AdditionalService();
                $additionalService->periodPurchasedDec($orderInfo);
            }
            if ($params['refund_status'] == 3) $updateData['refund_reject_time'] = time();
            $updateData['refund_status'] = $params['refund_status'];
        }
        if (!empty($params['express_type']) || !empty($params['consignee']) || !empty($params['consignee_phone']) || !empty($params['province_id']) || !empty($params['city_id']) || !empty($params['district_id']) || !empty($params['address'])) {
            if ($orderInfo['sub_order_status'] != 1) $this->throwError('当前订单状态不允许修改快递方式和物流地址');
        }
        if (!empty($params['express_type'])) $updateData['express_type'] = $params['express_type'];
        if (!empty($params['express_number'])) $updateData['express_number'] = trim($params['express_number']);
        if (!empty($params['return_number'])) $updateData['return_number'] = trim($params['return_number']);
        if (isset($params['is_after_sale']) && in_array($params['is_after_sale'], [0, 1])) $updateData['is_after_sale'] = $params['is_after_sale'];
        if (isset($params['work_order_status']) && in_array($params['work_order_status'], [0, 1, 2])) $updateData['work_order_status'] = $params['work_order_status'];
        if (isset($params['is_delete']) && is_numeric($params['is_delete'])) $updateData['is_delete'] = $params['is_delete'];
        if (isset($params['is_ts']) && is_numeric($params['is_ts'])) {
            if ($params['is_ts'] == 0) {
                $lock_order = CrossLockOrder::where([
                    'main_order_no' => $orderInfo['main_order_no'],
                    'status'        => 1,
                ])->find();
                if ($lock_order) {
                    $vinehoo_client = request()->header('vinehoo-client', '');
                    if (in_array($vinehoo_client, ['ios', 'android', 'h5', 'miniapp'])) {
                        $err_msg = '订单异常,无法立即发货,请联系客服处理。';
                    } else {
                        $err_msg = '该订单已锁定,锁定原因:' . ($lock_order['remark'] ?? '');
                    }
                    $this->throwError($err_msg, ErrorCode::PARAM_ERROR);
                }
                if (in_array($params['order_type'], [2])) {
                    if (($orderInfo['store_type'] == 2) && (strtotime('2024-10-01 00:00:00') > time())) {
                        if ($orderInfo['express_type'] != 3) {
                            $this->throwError('因发货地气温较高，此跨境订单暂时只能选择暂存或冷链发货。', ErrorCode::PARAM_ERROR);
                        }
                    }
                }

                // 处理预计发货时间 (predict_time)
                if (isset($orderInfo['predict_time']) && is_numeric($orderInfo['predict_time'])) {
                    $currentTime = time();
                    if ($orderInfo['predict_time'] < $currentTime) {
                        $currentHour = date('H', $currentTime);
                        if ($currentHour < 10) {
                            // 当前时间小于16点，设置为今天 23:59:59
                            $updateData['predict_time'] = strtotime(date('Y-m-d') . ' 23:59:59');
                        } else {
                            // 当前时间大于等于16点，设置为明天 23:59:59
                            $updateData['predict_time'] = strtotime(date('Y-m-d', $currentTime + 86400) . ' 23:59:59');
                        }
                    }
                }
            }
            if (in_array($params['order_type'], [7])) {
                //三方订单
                if ($orderInfo['push_wms_status'] == 1) {
                    //同步更新萌芽暂存状态
                    $wms_up_data = [
                        'orderno' => $orderInfo['sub_order_no'],//商家订单号
                        'storage' => $params['is_ts'], //是否暂存（0否，1是）
                    ];
                    \Curl::receiptInfo($wms_up_data);
                }
            }

            $updateData['is_ts'] = $params['is_ts'];
            $remarks             = array(
                'sub_order_no' => $orderInfo['sub_order_no'],
                'admin_id'     => $params['operator'],
                'remarks'      => $params['is_ts'] == 0 ? '订单已取消暂存' : '订单已设置暂存',
                'created_time' => time(),
            );

            // 处理暂存时间 ts_time，逻辑与 stagingOrder 方法保持一致
            if (isset($params['ts_time']) || ($params['is_ts'] == 0)) {
                $p_ts_time = empty($params['ts_time']) ? 0 : strtotime($params['ts_time']);
                $ts_log    = Db::table('vh_orders.vh_order_ts')->where('sub_order_no', $orderInfo['sub_order_no'])->find() ?? [];
                $ts_update = [
                    'sub_order_no' => $orderInfo['sub_order_no'],
                    'order_type'   => $params['order_type'],
                    'ts_time'      => $p_ts_time,
                    'admin_id'     => $params['operator'] ?? null,
                    'uid'          => $ts_log['uid'] ?? ($orderInfo['uid'] ?? 0),
                    'remarks'      => ($ts_log['remarks'] ?? '') . " 管理员设置:暂存时间",
                    'created_time' => $ts_log['created_time'] ?? time(),
                    'update_time'  => time(),
                    'status'       => $params['is_ts'] == 1 ? 0 : 3,
                ];
                if (empty($ts_log)) {
                    Db::table('vh_orders.vh_order_ts')->insert($ts_update);
                } else {
                    Db::table('vh_orders.vh_order_ts')->where('sub_order_no', $orderInfo['sub_order_no'])->update($ts_update);
                }
                unset($params['ts_time']);
            }

            //取消暂存推送群消息
            if ($params['order_type'] == 2 && $params['is_ts'] == 0) {
                $store      = $orderInfo['store_type'] == 1 ? '古斯缇' : '南沙';
                $periodInfo = esGetOne($orderInfo['period'], 'vinehoo.periods');
                $content    = "# 跨境订单用户取消暂存提示\n";
                $content    .= "-主订单号：" . $orderInfo['main_order_no'] . "\n";
                $content    .= "-子订单号：" . $orderInfo['sub_order_no'] . "\n";
                $content    .= "-订单仓库：" . $store . "\n";
                $content    .= "-期数：" . $orderInfo['period'] . "\n";
                $content    .= "-预计发货时间：" . date('Y-m-d H:i:s', $orderInfo['predict_time']) . "\n";
                $content    .= "-品名：" . $periodInfo['title'] . "\n";
                $queueData  = array(
                    'access_token' => env('ORDERS.cross_token'),
                    'type'         => 'text',
                    'at'           => '***********,***********,***********',
                    'content'      => base64_encode($content),
                );
                $data       = base64_encode(json_encode($queueData));
                $pushData   = array(
                    'exchange_name' => 'dingtalk',
                    'routing_key'   => 'dingtalk_sender',
                    'data'          => $data,
                );
                httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
            }
            //取消暂存自动推送发货仓
//            if ($params['order_type'] == 2 && $orderInfo['is_auto_push'] == 1 && $params['is_ts'] == 0) {
//                $crossPushData = array(
//                    'exchange_name' => 'orders',
//                    'routing_key'   => 'cross_auto_push',
//                    'data'          => base64_encode(json_encode(['main_order_no' => $orderInfo['main_order_no']]))
//                );
//                httpPostString(env('ITEM.QUEUE_URL'), json_encode($crossPushData));
//            }
        }
        if (isset($params['push_t_status']) && is_numeric($params['push_t_status'])) $updateData['push_t_status'] = $params['push_t_status'];
        if (isset($params['push_wms_status']) && is_numeric($params['push_wms_status'])) $updateData['push_wms_status'] = $params['push_wms_status'];
        if (isset($params['payment_doc']) && is_numeric($params['payment_doc'])) $updateData['payment_doc'] = $params['payment_doc'];
        if (isset($params['push_store_status']) && is_numeric($params['push_store_status'])) $updateData['push_store_status'] = $params['push_store_status'];
        if (isset($params['work_nums']) && is_numeric($params['work_nums'])) $updateData['work_nums'] = $params['work_nums'] + $orderInfo['work_nums'];
        if (!empty($params['refund_money'])) $updateData['refund_money'] = trim($params['refund_money']);
        if (!empty($params['consignee'])) {
            //用户信息加密处理
            $consignee                      = trim($params['consignee']);
            $encrypt                        = cryptionDeal(1, [$consignee], $params['operator'], '前端用户');
            $consignee                      = isset($encrypt[$consignee]) ? $encrypt[$consignee] : '';
            $updateReceiveData['consignee'] = $consignee;
        }
        if (!empty($params['consignee_phone'])) {
            //用户信息加密处理
            $phone                                = trim($params['consignee_phone']);
            $encrypt                              = cryptionDeal(1, [$phone], $params['operator'], '前端用户');
            $phone                                = isset($encrypt[$phone]) ? $encrypt[$phone] : '';
            $updateReceiveData['consignee_phone'] = $phone;
        }
        if (!empty($params['province_id'])) $updateReceiveData['province_id'] = $params['province_id'];
        if (!empty($params['city_id'])) $updateReceiveData['city_id'] = $params['city_id'];
        if (!empty($params['district_id'])) $updateReceiveData['district_id'] = $params['district_id'];
        if (!empty($params['address'])) $updateReceiveData['address'] = trim($params['address']);

        if (!empty($updateReceiveData) && in_array($params['order_type'], [7])) {
            //三方订单更新主表
            if (!empty($updateReceiveData['consignee'])) $updateMainData['consignee'] = $updateReceiveData['consignee'];
            if (!empty($updateReceiveData['consignee_phone'])) $updateMainData['consignee_phone'] = $updateReceiveData['consignee_phone'];
            $i_address   = [];
            $sub_address = [];
            if (!empty($params['province_name'])) {
                $sub_address['province'] = $params['province_name'];
                $i_address[]             = $params['province_name'];
            }
            if (!empty($params['city_name'])) {
                $sub_address['city'] = $params['city_name'];
                $i_address[]         = $params['city_name'];
            }
            if (!empty($params['district_name'])) {
                $sub_address['district'] = $params['district_name'];
                $i_address[]             = $params['district_name'];
            }
            if (!empty($updateReceiveData['address'])) {
                $sub_address['address'] = $params['address'];
                $i_address[]            = $updateReceiveData['address'];
            }
            if (!empty($i_address)) {
                $updateMainData['address'] = implode('', $i_address);
            }

            if (Db::name('tripartite_order')->where([
                ['main_order_id', '=', $orderInfo['main_order_id']],
                ['push_wms_status', '=', 1],
            ])->count()) {
                $this->throwError('修改地址失败: 已有子订单成功推送萌芽,不可更改!');
            }

            if (!empty($sub_address)) {
                Db::name('tripartite_order')->where('sub_order_no', $orderInfo['sub_order_no'])->update($sub_address);

                $remark_info_enc = [
                    'consignee'       => $updateReceiveData['consignee'] ?? $orderInfo['consignee'],
                    'consignee_phone' => $updateReceiveData['consignee_phone'] ?? $orderInfo['consignee_phone'],
                ];
                $remark_info_dec = \Curl::cryptionDeal(array_values($remark_info_enc));
                $remark_info     = [
                    $remark_info_dec[$remark_info_enc['consignee']],
                    $remark_info_dec[$remark_info_enc['consignee_phone']],
                    $updateMainData['address'] ?? $orderInfo['address'],
                ];
                $remarks         = [
                    'sub_order_no' => $orderInfo['sub_order_no'],
                    'admin_id'     => $params['operator'],
                    'remarks'      => '修改地址: ' . implode(' ', $remark_info),
                    'created_time' => time(),
                ];
            }
        }
        //获取订单萌牙推送状态
        $push_wms_status = 0;
        if (in_array($params['order_type'], [0, 1, 3, 4, 8])) {
            $push_wms_status = isset($orderInfo['push_wms_status']) ? $orderInfo['push_wms_status'] : 0;
            $push_wms_data   = [];
            //获取仓库编码配置
            $config_warehouse = config('config')['warehouse'];
            $wms_id           = 1;
            foreach ($config_warehouse as &$val) {
                if (in_array($orderInfo['warehouse_code'], explode(',', $val['code']))) $wms_id = $val['value'];
            }
            if ($push_wms_status == 1 && $wms_id == 1) {
                if (isset($params['express_type']) && is_numeric($params['express_type'])) {
                    $push_wms_data['logistics_id'] = $params['express_type'];
                }
                if (!empty($params['consignee'])) {
                    $push_wms_data['receiver_name'] = $params['consignee'];
                }
                if (!empty($params['consignee_phone'])) {
                    $push_wms_data['receiver_phone'] = $params['consignee_phone'];
                }
                if (!empty($params['province_id'])) {
                    $province = $this->httpGet(env('ITEM.USER_URL') . '/user/v3/regional/getInfo', ['id' => $params['province_id']]);
                    if ($province['error_code'] != 0) $this->throwError('获取省信息失败');
                    $push_wms_data['province'] = $province['data']['name'];
                }
                if (!empty($params['city_id'])) {
                    $city = $this->httpGet(env('ITEM.USER_URL') . '/user/v3/regional/getInfo', ['id' => $params['city_id']]);
                    if ($city['error_code'] != 0) $this->throwError('获取市信息失败');
                    $push_wms_data['city'] = $city['data']['name'];
                }
                if (!empty($params['district_id'])) {
                    $district = $this->httpGet(env('ITEM.USER_URL') . '/user/v3/regional/getInfo', ['id' => $params['district_id']]);
                    if ($district['error_code'] != 0) $this->throwError('获取区信息失败');
                    $push_wms_data['town'] = $district['data']['name'];
                }
                if (!empty($params['address'])) {
                    $push_wms_data['address'] = $params['address'];
                }
                if (isset($params['is_ts']) && is_numeric($params['is_ts'])) {
                    $push_wms_data['storage'] = $params['is_ts'] == 1 ? 1 : 0;
                }
                //同步退货单号
                if (!empty($params['return_number'])) {
                    $return_wms_data = array(
                        'store_code'   => env('ORDERS.STORE_CODE'),
                        'orderno'      => $orderInfo['sub_order_no'],
                        'logistics_no' => [$params['return_number']],
                    );
                    $syncRes         = $this->httpPost(env("ITEM.DISTRIBUTE_URL") . "/sync/logisticsNo", $return_wms_data, ["Content-Type" => "application/json"]);
                    if (isset($syncRes['status'])) {
                        if ($syncRes['status'] == 'fail') {
                            Log::error('同步萌牙退货单号异常：' . $syncRes['msg']);
                        }
                    }
                }
            } else {
                //未推送发货仓的订单处理
                if (isset($params['is_ts']) && $params['is_ts'] == 0) {
                    //添加超时任务
                    $pushData = array(
                        'namespace' => "orders",
                        'key'       => "ts_push_" . $orderInfo['sub_order_no'],
                        'data'      => base64_encode(json_encode(['sub_order_no' => $orderInfo['sub_order_no'], 'order_type' => $orderInfo['order_type']])),
                        'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/push/pushWms',
                        'timeout'   => "1m",
                    );
                    $dealPush = $this->httpPost(env('ITEM.TIMEING_SERVICE_URL') . '/services/v3/timing/add', $pushData);
                    //任务日志
                    $taskLog = '推送发货仓超时任务日志：' . $orderInfo['sub_order_no'] . '请求参数：' . json_encode($pushData, JSON_UNESCAPED_UNICODE) . '返回参数：' . json_encode($dealPush, JSON_UNESCAPED_UNICODE);
                    file_put_contents(root_path() . "/runtime/log/" . date('Ym') . "/" . date('Ymd') . 'timingLog' . '.log', $taskLog . PHP_EOL, FILE_APPEND);
                }
            }
        }
        Db::startTrans();
        try {
            //子订单修改
            if (!empty($updateData)) {
                if ($params['order_type'] != 8) {
                    $updateData['operator'] = $params['operator'];
                }
                $updateData['update_time'] = time();
                $subResult                 = Db::name($order_type[$params['order_type']]['table'])->where(['id' => $orderInfo['id']])->update($updateData);
                if (empty($subResult)) $this->throwError('子订单修改失败');
            }
            //主订单修改
            if (!empty($updateMainData)) {
                $updateMainData['operator']    = $params['operator'];
                $updateMainData['update_time'] = time();
                $mainResult                    = Db::name('order_main')->where(['id' => $orderInfo['main_order_id']])->update($updateMainData);
                if (empty($mainResult)) $this->throwError('主订单修改失败');
            }
            //子订单收货信息修改
            if (!empty($updateReceiveData) && !in_array($params['order_type'], [7])) {
                $province_name                      = Db::table('vh_user.vh_regional')->where(['id' => $updateReceiveData['province_id']])->value('name');
                $city_name                          = Db::table('vh_user.vh_regional')->where(['id' => $updateReceiveData['city_id']])->value('name');
                $district_name                      = Db::table('vh_user.vh_regional')->where(['id' => $updateReceiveData['district_id']])->value('name');
                $updateReceiveData['province_name'] = !empty($province_name) ? $province_name : '';
                $updateReceiveData['city_name']     = !empty($city_name) ? $city_name : '';
                $updateReceiveData['district_name'] = !empty($district_name) ? $district_name : '';
                $isset                              = Db::name('sub_order_receive_information')->where(['sub_order_no' => $orderInfo['sub_order_no'], 'uid' => $orderInfo['uid'], 'main_order_no' => $orderInfo['main_order_no']])->count();
                if ($isset == 0) {
                    $insertReceiveData = array(
                        'uid'             => $orderInfo['uid'],
                        'main_order_no'   => $orderInfo['main_order_no'],
                        'sub_order_no'    => $orderInfo['sub_order_no'],
                        'order_type'      => $orderInfo['order_type'],
                        'province_id'     => !empty($updateReceiveData['province_id']) ? $updateReceiveData['province_id'] : $orderInfo['province_id'],
                        'city_id'         => !empty($updateReceiveData['city_id']) ? $updateReceiveData['city_id'] : $orderInfo['city_id'],
                        'district_id'     => !empty($updateReceiveData['district_id']) ? $updateReceiveData['district_id'] : $orderInfo['district_id'],
                        'address'         => !empty($updateReceiveData['address']) ? $updateReceiveData['address'] : $orderInfo['address'],
                        'consignee'       => !empty($updateReceiveData['consignee']) ? $updateReceiveData['consignee'] : $orderInfo['consignee'],
                        'consignee_phone' => !empty($updateReceiveData['consignee_phone']) ? $updateReceiveData['consignee_phone'] : $orderInfo['consignee_phone'],
                        'province_name'   => $updateReceiveData['province_name'],
                        'city_name'       => $updateReceiveData['city_name'],
                        'district_name'   => $updateReceiveData['district_name'],
                        'created_time'    => time(),
                    );
                    $insertReceive     = Db::name('sub_order_receive_information')->insert($insertReceiveData);
                    if (empty($insertReceive)) $this->throwError('订单收货信息修改失败');
                } else {
                    $updateReceiveData['update_time'] = time();
                    $updateReceive                    = Db::name('sub_order_receive_information')->where(['sub_order_no' => $orderInfo['sub_order_no'], 'uid' => $orderInfo['uid'], 'main_order_no' => $orderInfo['main_order_no']])->update($updateReceiveData);
                    if (empty($updateReceive)) $this->throwError('订单收货信息修改失败');
                }
                //同步最新收货信息到子订单es
                $consignee_encrypt       = hidestr($params['consignee'], 1);
                $consignee_phone_encrypt = hidestr($params['consignee_phone'], 3, 4);
                $updateEsData            = [
                    "province_id"             => $updateReceiveData['province_id'],
                    "city_id"                 => $updateReceiveData['city_id'],
                    "district_id"             => $updateReceiveData['district_id'],
                    "address"                 => $updateReceiveData['address'],
                    "province_name"           => $updateReceiveData['province_name'],
                    "city_name"               => $updateReceiveData['city_name'],
                    "district_name"           => $updateReceiveData['district_name'],
                    "consignee"               => $updateReceiveData['consignee'],
                    "consignee_phone"         => $updateReceiveData['consignee_phone'],
                    "consignee_encrypt"       => $consignee_encrypt,
                    "consignee_phone_encrypt" => $consignee_phone_encrypt,
                ];
                $updateEs                = esUpdateData($orderInfo['sub_order_no'], 'vinehoo.orders', $updateEsData);
                if (empty($updateEs)) $this->throwError('订单收货信息修改失败');
            }
            if (!empty($remarks)) {
                Db::name('order_remarks')->insert($remarks);
            }
            if (!empty($params['erp_push_amount'])) {
                Db::name('sub_order_extend')->where(['sub_order_no' => $params['order_no'], 'order_type' => $params['order_type']])->update(['erp_push_amount' => $params['erp_push_amount'], 'update_time' => time()]);
            }
            if (isset($params['refund_status']) && ($params['refund_status'] == 2)) {
                (new AfterSalesService())->cancelGiftOrders($orderInfo['main_order_no']);
            }
            Db::commit();
            //同步修改发货单信息
            if ($push_wms_status == 1 && !empty($push_wms_data)) {
                $push_wms_data['store_code'] = env('ORDERS.STORE_CODE');
                $push_wms_data['orderno']    = $params['order_no'];
                $syncRes                     = curlRequest(env("ITEM.DISTRIBUTE_URL") . "/sync/shiporder/receiptInfo", $push_wms_data);
                if (isset($syncRes['status'])) {
                    if ($syncRes['status'] == 'fail') {
                        $this->throwError("您的宝贝已打包好，今天就能奔向您身边!别让期待落空哦~");
//                        $this->throwError('同步萌牙订单异常：' . $syncRes['msg']);
                    }
                } else {
                    $this->throwError("您的宝贝已打包好，今天就能奔向您身边!别让期待落空哦~");
//                    $this->throwError('同步萌牙订单异常：同步发货单收货地址异常');
                }
            }
            //验证状态过滤兔头订单
            if (isset($params['sub_order_status']) && $params['sub_order_status'] == 3 && $params['order_type'] != 4) {
                //订单完成发放兔头、经验值
                $pushUserData      = array(
                    'uid'       => $orderInfo['uid'],
                    'task_id'   => 8,
                    'orderno'   => $orderInfo['sub_order_no'],
                    'pay_money' => $orderInfo['payment_amount']
                );
                $sendTaskCompleted = httpPostString(env('ITEM.USER_URL') . '/user/v3/user/sendTaskCompleted', json_encode($pushUserData));
                if (!isset($sendTaskCompleted['error_code']) || $sendTaskCompleted['error_code'] != 0) {
                    Log::error($orderInfo['sub_order_no'] . '：发放兔头失败！' . $sendTaskCompleted['error_msg']);
                }
                //赠送优惠券
                if (!empty($packageInfo['coupons_id'])) {
                    $log = Db::name('order_receipt_issue_coupon_log')->where(['sub_order_no' => $orderInfo['sub_order_no']])->count();
                    if ($log == 0) {
                        $coupon_id_arr = explode('·', $packageInfo['coupons_id']);
                        foreach ($coupon_id_arr as &$val) {
                            $pushCouponData = array(
                                'uids'      => [$orderInfo['uid']],
                                'remark'    => $orderInfo['sub_order_no'] . '确认收货发放优惠券',
                                'coupon_id' => $val,
                            );
                            $dealCoupon     = httpPostString(env('ITEM.COUPON_URL') . '/coupon/v3/coupon/grant', json_encode($pushCouponData));
                            if (!isset($dealCoupon['error_code']) || $dealCoupon['error_code'] != 0) {
                                Log::error($orderInfo['sub_order_no'] . '：发放优惠券失败！' . $dealCoupon['error_msg']);
                            }
                            //记录日志
                            Db::name('order_receipt_issue_coupon_log')->insert(['sub_order_no' => $orderInfo['sub_order_no'], 'push_coupon_data' => json_encode($pushCouponData, JSON_UNESCAPED_UNICODE), 'request_param' => json_encode($dealCoupon, JSON_UNESCAPED_UNICODE), 'created_time' => time()]);
                        }
                    }
                }
                //去开票
                if (isset($orderInfo['invoice_progress']) && $orderInfo['invoice_progress'] == 1) {
                    //获取发票抬头信息
                    $invoiceInfo = Db::name('invoice_record')->where(['sub_order_no' => $orderInfo['sub_order_no']])->find();
                    if (!empty($invoiceInfo)) {
                        $invoiceData = array(
                            'uid'             => intval($orderInfo['uid']),
                            'order_info'      => [['sub_order_no' => $orderInfo['sub_order_no'], 'order_type' => $orderInfo['order_type']]],
                            'invoice_type'    => 1,
                            'invoice_name'    => $invoiceInfo['invoice_name'],
                            'type_id'         => $invoiceInfo['type_id'],
                            'email'           => $invoiceInfo['email'],
                            'taxpayer'        => $invoiceInfo['taxpayer'],
                            'telephone'       => $invoiceInfo['telephone'],
                            'company_address' => $invoiceInfo['company_address'] ?? '',
                            'company_tel'     => $invoiceInfo['company_tel'] ?? '',
                            'opening_bank'    => $invoiceInfo['opening_bank'] ?? '',
                            'bank_account'    => $invoiceInfo['bank_account'] ?? ''
                        );
                        //添加超时任务
                        $pushData = array(
                            'namespace' => 'orders',
                            'key'       => 'invoice_' . $orderInfo['sub_order_no'],
                            'data'      => base64_encode(json_encode($invoiceData, JSON_UNESCAPED_UNICODE)),
                            'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/orderInvoice/orderInvoice',
                            'timeout'   => "1m",
                        );
                        $this->httpPost(env('ITEM.TIMEING_SERVICE_URL') . '/services/v3/timing/add', $pushData);
                    }
                }
            }
        } catch (\Exception $e) {
            Db::rollback();
            Log::error(json_encode($requstparams, JSON_UNESCAPED_UNICODE) . '：公共修改订单数据失败！错误信息：' . $e->getMessage());
            $this->throwError($e->getMessage());
        }
        return true;
    }

    /**
     * Description:拍卖订单公共修改
     * Author: zrc
     * Date: 2023/3/16
     * Time: 16:39
     * @param $params
     * @return bool
     */
    public function updateOrderAuction($params)
    {
        $orderInfo = Db::table('vh_auction.vh_orders')->where(['order_no' => $params['order_no']])->find();
        if (empty($orderInfo)) $this->throwError('未获取到订单信息');
        $updateData = ['update_time' => time()];
        $wmsData    = [];
        if (isset($params['work_order_status']) && in_array($params['work_order_status'], [0, 1, 2])) {
            $updateData['work_order_status'] = $params['work_order_status'];
        }
        if (isset($params['refund_status']) && in_array($params['refund_status'], [0, 1, 2, 3])) {
            $updateData['refund_status'] = $params['refund_status'];
        }
        if (isset($params['is_after_sale']) && in_array($params['is_after_sale'], [0, 1])) {
            $updateData['is_after_sale'] = $params['is_after_sale'];
        }
        if (isset($params['sub_order_status']) && is_numeric($params['sub_order_status'])) {
            $updateData['order_status'] = $params['sub_order_status'];
            if ($params['sub_order_status'] == 2) $updateData['delivery_time'] = time();
            if ($params['sub_order_status'] == 3) $updateData['goods_receipt_time'] = time();
        }
        if (!empty($params['refund_money'])) {
            $updateData['refund_money'] = trim($params['refund_money']);
        }
        if (!empty($params['province_id'])) {
            $updateData['province_id'] = $params['province_id'];
        }
        if (!empty($params['city_id'])) {
            $updateData['city_id'] = $params['city_id'];
        }
        if (!empty($params['district_id'])) {
            $updateData['district_id'] = $params['district_id'];
        }
        if (!empty($params['address'])) {
            $updateData['address'] = $params['address'];
        }
        if (!empty($params['province_id']) && !empty($params['city_id']) && !empty($params['district_id']) && !empty($params['address'])) {
            if ($orderInfo['order_status'] > 1) $this->throwError('订单状态异常，修改失败');
            //获取地区信息
            $batchInfo = curlRequest(env('ITEM.USER_URL') . '/user/v3/regional/getBatchInfo', ['id' => $params['province_id'] . ',' . $params['city_id'] . ',' . $params['district_id']], [], 'GET');
            $batchInfo = json_decode($batchInfo, true);
            if (empty($batchInfo['data']['list'])) $this->throwError('未获取到地区信息');
            $updateData['province_name'] = $batchInfo['data']['list'][intval($params['province_id'])]['name'] ?? $orderInfo['province_name'];
            $updateData['city_name']     = $batchInfo['data']['list'][intval($params['city_id'])]['name'] ?? $orderInfo['city_name'];
            $updateData['district_name'] = $batchInfo['data']['list'][intval($params['district_id'])]['name'] ?? $orderInfo['district_name'];
            $updateData['address']       = $params['address'];
            $updateData['province_id']   = $params['province_id'];
            $updateData['city_id']       = $params['city_id'];
            $updateData['district_id']   = $params['district_id'];
            $wmsData['province']         = $updateData['province_name'];
            $wmsData['city']             = $updateData['city_name'];
            $wmsData['town']             = $updateData['district_name'];
            $wmsData['address']          = $updateData['address'];
        }
        if (!empty($params['consignee']) && !empty($params['consignee_phone'])) {
            if ($orderInfo['order_status'] > 1) $this->throwError('订单状态异常，修改失败');
            $consignee                     = trim($params['consignee']);
            $phone                         = trim($params['consignee_phone']);
            $encrypt                       = cryptionDeal(1, [$consignee, $phone], $params['uid'], '前端用户');
            $updateData['consignee']       = isset($encrypt[$consignee]) ? $encrypt[$consignee] : $orderInfo['consignee'];
            $updateData['consignee_phone'] = isset($encrypt[$phone]) ? $encrypt[$phone] : $orderInfo['consignee_phone'];
            $wmsData['receiver_name']      = $params['consignee'];
            $wmsData['receiver_phone']     = $params['consignee_phone'];
        }
        if (isset($params['express_type']) && in_array($params['express_type'], [2, 3, 4, 5, 6, 10, 21, 22, 23])) {
            if ($orderInfo['order_status'] > 1) $this->throwError('订单状态异常，修改失败');
            $updateData['express_type'] = $params['express_type'];
            $wmsData['logistics_id']    = $params['express_type'];
        }
        Db::startTrans();
        try {
            //收货信息修改，记录日志
            if (!empty($wmsData)) {
                $old_data = array(
                    'province_name'   => $orderInfo['province_name'],
                    'city_name'       => $orderInfo['city_name'],
                    'district_name'   => $orderInfo['district_name'],
                    'address'         => $orderInfo['address'],
                    'consignee'       => $orderInfo['consignee'],
                    'consignee_phone' => $orderInfo['consignee_phone'],
                    'express_type'    => $orderInfo['express_type'],
                );
                $log      = array(
                    'order_no'     => $params['order_no'],
                    'old_data'     => json_encode($old_data, JSON_UNESCAPED_UNICODE),
                    'new_data'     => json_encode($wmsData, JSON_UNESCAPED_UNICODE),
                    'admin_id'     => $params['uid'],
                    'created_time' => time()
                );
                Db::table('vh_auction.vh_order_edit_log')->insert($log);
            }
            Db::table('vh_auction.vh_orders')->where(['id' => $orderInfo['id']])->update($updateData);
            Db::commit();
            //获取仓库编码配置
            $config_warehouse = config('config')['warehouse'];
            $wms_id           = 1;
            foreach ($config_warehouse as &$val) {
                if (in_array($orderInfo['warehouse_code'], explode(',', $val['code']))) $wms_id = $val['value'];
            }
            //修改订单收货信息同步萌牙
            if ($orderInfo['push_wms_status'] == 1 && $wms_id == 1 && !empty($wmsData)) {
                $wmsData['store_code'] = env('ORDERS.STORE_CODE');
                $wmsData['orderno']    = $orderInfo['order_no'];
                $wmsDeal               = curlRequest(env('ITEM.OUTBOUND_URL') . '/sync/shiporder/receiptInfo', json_encode($wmsData, JSON_UNESCAPED_UNICODE), [], 'POST');
                if (!isset($wmsDeal['errorCode']) || $wmsDeal['errorCode'] != 0) {
                    $this->throwError('订单信息同步萌牙异常：' . $wmsDeal['msg']);
                }
            }
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError('修改订单信息失败!' . $e->getMessage());
        }
        return true;
    }

    /**
     * Description:获取订单系统配置信息
     * Author: zrc
     * Date: 2021/9/6
     * Time: 14:11
     * @param $requestparams
     */
    public function getConfig($requestparams)
    {
        $params = $requestparams;

        if (empty($params['config_key']) || $params['config_key'] == 'warehouse_code') {
            // 虚拟仓查询数据库
            $warehouse_code = Db::table('vh_commodities.vh_virtual_warehouse')->column('erp_id as value,virtual_name as label,physical_id');
        }

        if (empty($params['config_key']) || $params['config_key'] == 'tripartite_sg_store') {
            // 松鸽店铺查询数据库
            $tripartite_sg_store = Db::table('vh_push_t_plus.vh_customer_code')
                ->where('state', 1)
                ->where('company_id', 4)
                ->column('owner_id as value,name as label');
        }
        
        if (!empty($params['config_key'])) {
            $result = config('config.' . $params['config_key']);

            switch ($params['config_key']) {
                case 'warehouse_code':
                    // 虚拟仓查询数据库
                    $result = $warehouse_code;
                    break;
                case 'tripartite_sg_store':
                    // 松鸽店铺
                    $result = $tripartite_sg_store;
                    break;
            }
            
        } else {
            $result = config('config');
            // 虚拟仓查询数据库
            $result['warehouse_code'] = $warehouse_code;
            // 松鸽店铺
            $result['tripartite_sg_store'] = $tripartite_sg_store;
        }
        if (empty($result)) {
            $this->throwError('未获取到相关配置');
        }

        return $result;
    }

    /**
     * Description:订单确认收货处理
     * Author: zrc
     * Date: 2021/11/11
     * Time: 10:01
     * @param $requestparams
     * @return bool|void
     * @throws \think\db\exception\DbException
     */
    public function goodsReceipt($requestparams)
    {
        $params = $requestparams;
        if (strpos($params['sub_order_no_str'], 'VHA') !== false) {
            $auctionResult = curlRequest(env('ITEM.AUCTION_ORDERS_URL') . '/auction-order/v3/order/confirmReceipt', ['order_no' => $params['sub_order_no_str'], 'uid' => $params['operator']]);
            if ($auctionResult['error_code'] != 0) $this->throwError($auctionResult['error_msg']);
            return true;
        }
        $order_type = config('config')['order_type'];//订单频道获取
        $es         = new ElasticSearchService();
        $arr        = array(
            'index' => ['orders'],
            'match' => [['sub_order_no.keyword' => $params['sub_order_no_str']]],
            'limit' => 1,
        );
        $data       = $es->getDocumentList($arr);
        if (!isset($data['data'][0])) $this->throwError('未获取到订单详情');
        $orderInfo        = $data['data'][0];
        $sub_order_status = Db::name($order_type[$orderInfo['order_type']]['table'])->where(['sub_order_no' => $orderInfo['sub_order_no']])->value('sub_order_status');
        if ($sub_order_status != 2) {
            if ($sub_order_status == 3) {
                return true;
            }
            $this->throwError($params['sub_order_no_str'] . '订单状态异常');
        }
        //修改订单状态
        $result = Db::name($order_type[$orderInfo['order_type']]['table'])->where(['sub_order_no' => $orderInfo['sub_order_no']])->update(['sub_order_status' => 3, 'goods_receipt_time' => time(), 'operator' => $params['operator']]);
        if (empty($result)) $this->throwError($orderInfo['sub_order_no'] . '修改订单状态失败');
        //过滤兔头订单
        if ($orderInfo['order_type'] != 4) {
            //去开票
            if (isset($orderInfo['invoice_progress']) && $orderInfo['invoice_progress'] == 1) {
                //获取发票抬头信息
                $invoiceInfo = Db::name('invoice_record')->where(['sub_order_no' => $orderInfo['sub_order_no']])->find();
                if (!empty($invoiceInfo)) {
                    $invoiceData = array(
                        'uid'             => intval($orderInfo['uid']),
                        'order_info'      => json_encode([['sub_order_no' => $orderInfo['sub_order_no'], 'order_type' => $orderInfo['order_type']]], true),
                        'invoice_type'    => $invoiceInfo['invoice_type'],
                        'invoice_name'    => $invoiceInfo['invoice_name'],
                        'type_id'         => $invoiceInfo['type_id'],
                        'email'           => $invoiceInfo['email'],
                        'taxpayer'        => $invoiceInfo['taxpayer'],
                        'telephone'       => $invoiceInfo['telephone'] ?? '',
                        'company_address' => $invoiceInfo['company_address'] ?? '',
                        'company_tel'     => $invoiceInfo['company_tel'] ?? '',
                        'opening_bank'    => $invoiceInfo['opening_bank'] ?? '',
                        'bank_account'    => $invoiceInfo['bank_account'] ?? ''
                    );
                    //添加超时任务
                    $pushData = array(
                        'namespace' => 'orders',
                        'key'       => 'invoice_' . $orderInfo['sub_order_no'],
                        'data'      => base64_encode(json_encode($invoiceData, JSON_UNESCAPED_UNICODE)),
                        'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/orderInvoice/orderInvoice',
                        'timeout'   => "1m",
                    );
                    $this->httpPost(env('ITEM.TIMEING_SERVICE_URL') . '/services/v3/timing/add', $pushData);
                }
            }
            //赠送兔头、经验值
            if (isset($orderInfo['payment_amount']) && $orderInfo['payment_amount'] > 0) {
                $userPushData      = array(
                    'uid'       => $orderInfo['uid'],
                    'task_id'   => 8,//任务ID,8为购买商品
                    'orderno'   => $orderInfo['sub_order_no'],
                    'pay_money' => $orderInfo['payment_amount']
                );
                $sendTaskCompleted = httpPostString(env('ITEM.USER_URL') . '/user/v3/user/sendTaskCompleted', json_encode($userPushData));
                if (!isset($sendTaskCompleted) || $sendTaskCompleted['error_code'] != 0) {
                    Log::error($orderInfo['sub_order_no'] . '：赠送兔头失败！' . $sendTaskCompleted['error_msg']);
                }
            }
            //拼团赠送优惠券
            //获取套餐es的associated_products
            $packageInfo = esGetOne($orderInfo['package_id'], 'vinehoo.periods_set');
            if (empty($packageInfo)) $this->throwError('未获取到套餐信息');
            if (!empty($packageInfo['coupons_id'])) {
                $log = Db::name('order_receipt_issue_coupon_log')->where(['sub_order_no' => $orderInfo['sub_order_no']])->count();
                if ($log == 0) {
                    $coupon_id_arr = explode('.', $packageInfo['coupons_id']);
                    foreach ($coupon_id_arr as &$v) {
                        $pushCouponData = array(
                            'uids'      => [$orderInfo['uid']],
                            'remark'    => $orderInfo['sub_order_no'] . '确认收货发放优惠券',
                            'coupon_id' => $v,
                        );
                        $dealCoupon     = httpPostString(env('ITEM.COUPON_URL') . '/coupon/v3/coupon/grant', json_encode($pushCouponData));
                        if (!isset($dealCoupon) || $dealCoupon['error_code'] != 0) {
                            Log::error($orderInfo['sub_order_no'] . '：发放优惠券失败！' . $dealCoupon['error_msg']);
                        }
                        //记录日志
                        Db::name('order_receipt_issue_coupon_log')->insert(['sub_order_no' => $orderInfo['sub_order_no'], 'push_coupon_data' => json_encode($pushCouponData, JSON_UNESCAPED_UNICODE), 'request_param' => json_encode($dealCoupon, JSON_UNESCAPED_UNICODE), 'created_time' => time()]);
                    }
                }
            }
        }
        return true;
    }

    /**
     * Description:通过主订单获取订单信息（满赠需要）
     * Author: zrc
     * Date: 2021/12/18
     * Time: 12:15
     * @param $requestparams
     * @return array|Db|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOrderDetailByMainOrderNo($requestparams)
    {
        $params = $requestparams;
        $result = Db::name('order_main')->field('uid,payment_amount')->where(['main_order_no' => $params['main_order_no']])->find();
        if (empty($result)) $this->throwError('未获取到主订单信息');
        $result['list'] = [];
        $es             = new ElasticSearchService();
        $esData         = $es->getDocumentList(['index' => ['orders'], 'match' => [['main_order_no' => $params['main_order_no']]], 'source' => ['sub_order_no', 'period', 'order_type', 'payment_amount', 'order_qty']]);
        if (isset($esData['data'])) $result['list'] = $esData['data'];
        return $result;
    }

    /**
     * Description:满赠处理创建关联子订单
     * Author: zrc
     * Date: 2021/11/11
     * Time: 10:08
     * @param $requestparams
     * @return bool|void
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function fullGiftDeal($requestparams)
    {
        $params    = $requestparams;
        $es        = new ElasticSearchService();
        $orderInfo = $this->orderDetail(['order_no' => $params['related_order_no']]);
        if ($params['type'] == 1) {//自营平台
            //判断是否已经赠送赠品
            $esGiftData = $es->getDocumentList(['index' => ['orders'], 'match' => [['main_order_id' => $orderInfo['main_order_id']], ['is_gift' => 1]], 'source' => ['sub_order_no'], 'limit' => 1]);
            if (isset($esGiftData['data']) && count($esGiftData['data']) > 0) $this->throwError($params['related_order_no'] . '已赠送赠品');
            $items         = [];
            $main_order_no = creatOrderNo(env('ORDERS.ORDER_MAIN'), $orderInfo['uid']);
            Db::startTrans();
            try {
                foreach ($params['items_info'] as &$val) {
                    //数据验证
                    $validate = Validate::rule([
                        'period|期数'           => 'require',
                        'package_id|套餐ID'     => 'require',
                        'order_qty|数量'        => 'require|number',
                        'is_merge|是否合并发货' => 'require|in:0,1'
                    ]);
                    if (!$validate->check($val)) {
                        $this->throwError($validate->getError(), ErrorCode::PARAM_ERROR);
                    }
                    $esPeriodsData = $es->getDocumentList(['index' => ['periods'], 'match' => [['id' => $val['period']]], 'source' => ['periods_type'], 'limit' => 1]);
                    if (!isset($esPeriodsData['data'][0])) $this->throwError('未获取到期数信息');
                    $periods_type = $esPeriodsData['data'][0]['periods_type'];
                    if ($periods_type == 0) $channel = 'flash';
                    if ($periods_type == 1) $channel = 'second';
                    if ($periods_type == 2) $channel = 'cross';
                    if ($periods_type == 3) $channel = 'leftover';
                    $items[] = array(
                        'period'      => $val['period'],
                        'set_id'      => $val['package_id'],
                        'buy_num'     => $val['order_qty'],
                        'channel'     => $channel,
                        'mystery_box' => [],
                    );
                    try {
                        $erp_id = Db::table('vh_commodities.vh_periods_product_inventory')->where('period', $val['period'])->where('product_id', json_decode(Es::name(Es::PERIODS_PACKAGE)->where([['id', '==', $val['package_id']]])->field('associated_products')->find()['associated_products'], true)[0]['product_id'])->value('erp_id');
                    } catch (\Exception $e) {
                    }
                    $data = array(
                        'uid'                   => $orderInfo['uid'],
                        'sub_order_no'          => creatOrderNo(env('ORDERS.ORDER_SON'), $orderInfo['uid']),
                        'sub_order_status'      => 1,
                        'main_order_id'         => $orderInfo['main_order_id'],
                        'period'                => $val['period'],
                        'package_id'            => $val['package_id'],
                        'order_from'            => $orderInfo['order_from'],
                        'order_qty'             => $val['order_qty'],
                        'payment_amount'        => 0,
                        'express_fee'           => 0,
                        'money_off_split_value' => 0,
                        'express_type'          => $orderInfo['express_type'],
                        'is_gift'               => 1,
                        'is_ts'                 => $orderInfo['is_ts'],
                        'created_time'          => time(),
                        'order_type'            => $periods_type,
                        'predict_time'          => strtotime($orderInfo['predict_time']),
                        'warehouse_code'        => $erp_id ?? '',
                        'payment_time'          => time()
                    );
                    switch ($periods_type) {
                        case 0:
                            $table = 'flash_order';
                            break;
                        case 1:
                            $table = 'second_order';
                            break;
                        case 3:
                            $table = 'tail_order';
                            break;
                        default:
                            $this->throwError($params['related_order_no'] . '商品频道类型错误');
                            break;
                    }
                    $addOrder = Db::name($table)->insert($data);
                    if (empty($addOrder)) {
                        Db::rollback();
                        $this->throwError('赠品处理失败:创建赠品订单异常');
                    }
                }
                /**库存验证+扣除可用库存 start**/
                $stock_param = json_encode(['orderno' => $main_order_no, 'groupid' => 0, 'items' => $items]);
                $stockVerify = httpPostString(env('ITEM.VINEHOO_INVENTORY_INOUT_URL') . '/inventory_service/v3/inventory/dec', $stock_param);
                if ($stockVerify['error_code'] != 0) {
                    $this->throwError('商品库存不足！', 20107);
                }
                /**库存验证+扣除可用库存 end**/
                Db::commit();
                //赠品订单推送萌牙
                $sub_order_no = $data['sub_order_no'];
                $pushService  = new PushService();
                $pushService->pushWms(['sub_order_no' => $sub_order_no, 'order_type' => $periods_type]);
            } catch (\Exception $e) {
                Db::rollback();
                if (isset($stock_param)) {
                    //退还库存
                    httpPostString(env('ITEM.VINEHOO_INVENTORY_INOUT_URL') . '/inventory_service/v3/inventory/inc', $stock_param);
                }
                $this->throwError($e->getMessage());
            }
        } else if ($params['type'] == 2) {//三方订单
            $tripartiteOrder = Db::name('tripartite_order')->where(['sub_order_no' => $params['related_order_no']])->find();
            if (empty($tripartiteOrder)) {
                $this->throwError($params['related_order_no'] . '未获取到订单信息');
            }
            //是否已有赠品验证
            $items_info_arr = explode(',', $tripartiteOrder['items_info']);
            foreach ($items_info_arr as &$val) {
                $goodsArr = explode('*', $val);
                if ($goodsArr[2] == 0) {
                    $this->throwError($params['related_order_no'] . '已赠送赠品');
                }
            }
            //赠品信息追加到订单商品信息
            Db::startTrans();
            try {
                foreach ($params['items_info'] as &$value) {
                    if (empty($value['short_code'])) $this->throwError('简码必传', ErrorCode::PARAM_ERROR);
                    if (empty($value['warehouse_id'])) $this->throwError('仓库ID必传', ErrorCode::PARAM_ERROR);
                    if (empty($value['order_qty']) || !preg_match("/^[1-9][0-9]*$/", $value['order_qty'])) $this->throwError('数量必传', ErrorCode::PARAM_ERROR);
                    if (!isset($value['is_merge']) || !in_array($value['is_merge'], [0, 1])) $this->throwError('是否合并发货必传', ErrorCode::PARAM_ERROR);
                    if ($value['is_merge'] == 1) {
                        $items_info     = Db::name('tripartite_order')->where(['sub_order_no' => $params['related_order_no']])->value('items_info');
                        $new_items_info = $items_info . ',' . $value['short_code'] . '*' . $value['order_qty'] . '*0';
                        $result         = Db::name('tripartite_order')->where(['sub_order_no' => $params['related_order_no']])->update(['items_info' => $new_items_info]);
                        if (empty($result)) {
                            Db::rollback();
                            $this->throwError('赠品处理失败：修改订单信息异常');
                        }
                    } else {
                        $items_info = $value['short_code'] . '*' . $value['order_qty'] . '*0';
                        $postData   = ['short_code' => $value['short_code'], 'field' => 'cn_product_name'];
                        $products   = $this->httpGet(env('ITEM.WINE_WIKI_URL') . '/wiki/v3/product/query', $postData);
                        if ($products['error_code'] != 0) {
                            Db::rollback();
                            $this->throwError('获取简码商品信息失败');
                        }
                        $data     = array(
                            'sub_order_no'          => creatOrderNo(env('ORDERS.ORDER_SON'), ''),
                            'sub_order_status'      => $tripartiteOrder['sub_order_status'],
                            'main_order_id'         => $tripartiteOrder['main_order_id'],
                            'title'                 => $products['data']['cn_product_name'],
                            'items_info'            => $items_info,
                            'store_id'              => $tripartiteOrder['store_id'],
                            'warehouse_id'          => $value['warehouse_id'],
                            'order_from_thirdparty' => $tripartiteOrder['order_from_thirdparty'],
                            'order_qty'             => $value['order_qty'],
                            'payment_amount'        => 0,
                            'express_type'          => $tripartiteOrder['express_type'],
                            'order_type'            => 7,
                        );
                        $addOrder = Db::name('tripartite_order')->insert($data);
                        if (empty($addOrder)) {
                            Db::rollback();
                            $this->throwError('赠品处理失败:创建赠品订单异常');
                        }
                    }
                }
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                $this->throwError($e->getMessage());
            }
        }
        return true;
    }

    /**
     * Description:修改订单支付方式、支付主体
     * Author: zrc
     * Date: 2021/11/11
     * Time: 10:09
     * @param $requestparams
     * @return bool|void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updatePaymentMethod($requestparams)
    {
        $params = $requestparams;
        if (strpos($params['main_order_no'], 'VHP') !== false) {
            $winePartyOrder = $this->httpPost(env('ITEM.WINEPARTY_URL') . '/wineparty/v3/order/update', ['main_order_no' => $params['main_order_no'], 'operator' => 0, 'payment_method' => $params['payment_method'], 'payment_subject' => $params['payment_subject']]);
            if (!isset($winePartyOrder['error_code']) || $winePartyOrder['error_code'] != 0) $this->throwError('修改支付方式失败，请重试');
            return true;
        } elseif (strpos($params['main_order_no'], 'VHL') !== false) {
            $winePartyOrder = $this->httpPost(env('ITEM.USER_URL') . '/user/v3/giftCards/updatePaymentMethod', ['main_order_no' => $params['main_order_no'], 'operator' => 0, 'payment_method' => $params['payment_method'], 'payment_subject' => $params['payment_subject']]);
            if (!isset($winePartyOrder['error_code']) || $winePartyOrder['error_code'] != 0) $this->throwError('修改支付方式失败，请重试');
            return true;
        }
        $orderInfo = Db::name('order_main')->field('id,main_order_status,payment_method,order_type')->where(['main_order_no' => $params['main_order_no']])->find();
        if (empty($orderInfo)) $this->throwError('未获取到订单信息');
        if ($orderInfo['main_order_status'] != 0) $this->throwError('订单已超时取消');
        if (in_array($params['payment_method'], [12])) {
            $sub_orders = Es::name(Es::ORDERS)->where([['main_order_id', '==', $orderInfo['id']]])->field('sub_order_no,period')->select()->toArray();
            $periods    = Es::name(Es::PERIODS)->where([["_id", 'in', array_values(array_unique(array_column($sub_orders, 'period')))]])->field('id,payee_merchant_id')->select()->toArray();
            if (in_array(10, array_column($periods, 'payee_merchant_id'))) {
                $this->throwError('部分商品不支持华为支付！请使用“酒云网”小程序支付。');
            }
        }

        $result = Db::name('order_main')->where(['main_order_no' => $params['main_order_no']])->update(['payment_method' => $params['payment_method'], 'payment_subject' => $params['payment_subject'], 'update_time' => time()]);
        if (empty($result)) $this->throwError('修改支付方式失败，请重试');
        if ($orderInfo['order_type'] == 2 && isset($params['payment_request_raw_data'])) {
            Db::name('cross_order')->where(['main_order_id' => $orderInfo['id']])->update(['payment_request_raw_data' => $params['payment_request_raw_data'], 'update_time' => time()]);
        }
        return true;
    }

    /**
     * Description:个人中心订单列表
     * Author: zrc
     * Date: 2021/11/29
     * Time: 17:15
     * @param $requestparams
     */
    public function personalList($requestparams)
    {
        $page       = !empty($requestparams['page']) ? $requestparams['page'] : 1;
        $limit      = !empty($requestparams['limit']) ? $requestparams['limit'] : 10;
        $params     = $requestparams;
        $orderModel = new OrderModel();
        $orderLists = $orderModel->personalList($params, $page, $limit);
        return $orderLists;
    }

    /**
     * Description:在线客服订单列表
     * Author: zrc
     * Date: 2023/9/15
     * Time: 10:41
     * @param $params
     * @return array
     */
    public function onlineServiceOrderList($params)
    {
        $result  = [];
        $where   = [];
        $page    = $params['page'];
        $limit   = $params['limit'];
        $where[] = ['term' => ['uid' => $params['uid']]];
        $where[] = ['term' => ['is_delete' => 0]];
        if (isset($params['is_ts']) && $params['is_ts'] == 1) {
            $where[] = ['term' => ['is_ts' => 1]];
        }
        $where[] = ['terms' => ['order_type' => [0, 1, 2, 3, 9, 11]]];
        $data    = esGetList('vinehoo.orders', $where, [['created_time' => 'desc']], 0, 1000);
        $list    = [];
        if (isset($data['hits']['hits'])) {
            foreach ($data['hits']['hits'] as &$val) {
                $orderData = $val['_source'];
                if ($orderData['sub_order_status'] == 0 && isset($orderData['main_order_id'])) {
                    if (isset($list[$orderData['main_order_id']])) {
                        $list[$orderData['main_order_id']]['goodsInfo'][] = [
                            'period'        => $orderData['period'],
                            'package_id'    => $orderData['package_id'],
                            'goods_img'     => imagePrefix($orderData['banner_img']),
                            'goods_title'   => $orderData['title'],
                            'package_name'  => $orderData['package_name'],
                            'order_qty'     => $orderData['order_qty'],
                            'package_price' => isset($orderData['special_price']) ? $orderData['special_price'] : $orderData['package_price'],
                            'periods_type'  => $orderData['order_type']
                        ];
                        $list[$orderData['main_order_id']]['total_qty']   = $list[$orderData['main_order_id']]['total_qty'] + $orderData['order_qty'];
                    } else {
                        $list[$orderData['main_order_id']] = [
                            'order_no'       => $orderData['main_order_no'],
                            'status'         => $orderData['sub_order_status'],
                            'goodsInfo'      => [[
                                'period'        => $orderData['period'],
                                'package_id'    => $orderData['package_id'],
                                'goods_img'     => imagePrefix($orderData['banner_img']),
                                'goods_title'   => $orderData['title'],
                                'package_name'  => $orderData['package_name'],
                                'order_qty'     => $orderData['order_qty'],
                                'package_price' => isset($orderData['special_price']) ? $orderData['special_price'] : $orderData['package_price'],
                                'periods_type'  => $orderData['order_type']
                            ]],
                            'created_time'   => $orderData['created_time'],
                            'total_qty'      => $orderData['order_qty'],
                            'payment_amount' => $orderData['order_type'] == 4 ? $orderData['main_rabbit_payment_amount'] : $orderData['main_payment_amount'],
                            'order_type'     => $orderData['order_type']
                        ];
                    }
                } else {
                    //订单状态处理 订单状态 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消 5-拼团中 6-已暂存 7-退款中 8-退款成功
                    //group_status拼团状态：0-不拼团 1-拼团中 2-拼团成功 3-拼团失败
                    if (isset($orderData['group_status']) && $orderData['group_status'] == 1 && $orderData['sub_order_status'] == 1) $orderData['sub_order_status'] = 5;
                    if (isset($orderData['is_ts']) && $orderData['is_ts'] == 1 && $orderData['sub_order_status'] == 1) $orderData['sub_order_status'] = 6;
                    if (isset($orderData['refund_status']) && $orderData['refund_status'] == 1) $orderData['sub_order_status'] = 7;
                    if (isset($orderData['refund_status']) && $orderData['refund_status'] == 2) $orderData['sub_order_status'] = 8;
                    $list[] = [
                        'order_no'       => $orderData['sub_order_no'],
                        'status'         => $orderData['sub_order_status'],
                        'goodsInfo'      => [[
                            'period'        => $orderData['period'],
                            'package_id'    => $orderData['package_id'],
                            'goods_img'     => imagePrefix($orderData['banner_img']),
                            'goods_title'   => $orderData['title'],
                            'package_name'  => $orderData['package_name'],
                            'order_qty'     => $orderData['order_qty'],
                            'package_price' => isset($orderData['special_price']) ? $orderData['special_price'] : $orderData['package_price'],
                            'periods_type'  => $orderData['order_type']
                        ]],
                        'created_time'   => $orderData['created_time'],
                        'total_qty'      => $orderData['order_qty'],
                        'payment_amount' => $orderData['order_type'] == 4 ? $orderData['rabbit_payment_amount'] : $orderData['payment_amount'],
                        'order_type'     => $orderData['order_type']
                    ];
                }
            }
        }
        $created_time = array_column($list, 'created_time');
        array_multisort($created_time, SORT_DESC, $list);
        $offset          = ($page - 1) * $limit;
        $result['list']  = array_slice($list, $offset, $limit);
        $result['total'] = count($list);
        return $result;
    }

    /**
     * Description:个人中心订单详情
     * Author: zrc
     * Date: 2021/12/2
     * Time: 16:52
     * @param $requestparams
     * @return mixed
     */
    public function personalDetail($requestparams)
    {
        $params     = $requestparams;
        $orderModel = new OrderModel();
        $data       = $orderModel->personalDetail($params);
        if (empty($data)) $this->throwError('未获取到订单详情');
        return $data;
    }

    /**
     * Description:已售\已购按钮获取列表
     * Author: zrc
     * Date: 2021/12/10
     * Time: 10:13
     * @param $requestparams
     */
    public function getSoldPurchasedOrderList($requestparams)
    {
        $params     = $requestparams;
        $page       = !empty($requestparams['page']) ? $requestparams['page'] : 1;
        $limit      = !empty($requestparams['limit']) ? $requestparams['limit'] : 10;
        $orderModel = new OrderModel();
        $orderLists = $orderModel->getSoldPurchasedOrderList($params, $page, $limit);
        return $orderLists;
    }

    /**
     * Description:用户取消、删除订单
     * Author: zrc
     * Date: 2021/12/13
     * Time: 17:08
     * @param $requestparams
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function cancelDeleteOrder($requestparams)
    {
        $params = $requestparams;
        if ($params['type'] == 1) {//取消订单
            $orderInfo = Db::name('order_main')->field('id,main_order_status')->where(['main_order_no' => $params['order_no'], 'uid' => $params['uid']])->find();
            if (empty($orderInfo)) $this->throwError('未获取到订单信息');
            if ($orderInfo['main_order_status'] != 0) $this->throwError('当前订单状态不允许取消');
            $this->timeOutOrderDeal(json_encode(['main_order_no' => $params['order_no']]));
        } else if ($params['type'] == 2) {//删除订单
            if (strpos($params['order_no'], 'VHM') !== false || strpos($params['order_no'], 'WYM') !== false) {
                $orderInfo = Db::name('order_main')->field('id,main_order_status,order_type')->where(['main_order_no' => $params['order_no']])->find();
                if (empty($orderInfo)) $this->throwError('未获取到订单信息');
                if (!in_array($orderInfo['main_order_status'], [3, 4])) $this->throwError('当前订单状态不允许删除');
                $order_type     = config('config')['order_type'];//订单频道获取
                $order_type_arr = explode(',', $orderInfo['order_type']);
                foreach ($order_type_arr as &$val) {
                    Db::name($order_type[intval($val)]['table'])->where(['main_order_id' => $orderInfo['id']])->update(['is_delete' => 1, 'update_time' => time()]);
                }
            } else if (strpos($params['order_no'], 'VHA') !== false) {
                $orderInfo = Db::table('vh_auction.vh_orders')->field('order_status,is_delete,refund_status')->where(['order_no' => $params['order_no']])->find();
                if (empty($orderInfo)) $this->throwError('未获取到订单信息');
                if ($orderInfo['is_delete'] == 1) $this->throwError('订单已删除请勿重复操作');
                if (in_array($orderInfo['order_status'], [0, 1, 2]) || $orderInfo['refund_status'] == 1) $this->throwError('当前订单状态不允许删除');
                Db::table('vh_auction.vh_orders')->where(['order_no' => $params['order_no']])->update(['is_delete' => 1, 'update_time' => time()]);
            } else {
                $orderInfo = $this->orderDetail(['order_no' => $params['order_no'], 'field' => 'sub_order_status,is_delete,order_type,refund_status']);
                if (!isset($orderInfo['refund_status'])) $orderInfo['refund_status'] = 0;
                if (in_array($orderInfo['sub_order_status'], [0, 1, 2]) || $orderInfo['refund_status'] == 1) $this->throwError('当前订单状态不允许删除');
                if ($orderInfo['is_delete'] == 1) $this->throwError('订单已删除请勿重复操作');
                $updateData = array(
                    'order_no'   => $params['order_no'],
                    'order_type' => $orderInfo['order_type'],
                    'operator'   => $params['uid'],
                    'is_delete'  => 1,
                );
                $this->updateOrder($updateData);
            }
        }
        return true;
    }

    /**
     * Description:订单套餐重绑
     * Author: zrc
     * Date: 2022/2/28
     * Time: 14:02
     * @param $requestparams
     * @return int
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function rebindOrderPackage($requestparams)
    {
        $params = $requestparams;
        //验证订单信息
        $order_type       = config('config')['order_type'];//订单频道获取
        $field            = 'period,package_id,sub_order_status,order_qty,push_wms_status';
        $sub_order_no_arr = explode(',', $params['sub_order_no']);
        if (count($sub_order_no_arr) > 1) $this->throwError('请传入单个订单号进行处理');
        Db::startTrans();
        try {
            foreach ($sub_order_no_arr as &$val) {
                $orderInfo = Db::name($order_type[intval($params['order_type'])]['table'])->field($field)->where(['sub_order_no' => $val])->find();
                if (empty($orderInfo)) $this->throwError($val . '未获取到订单信息');
                if ($orderInfo['sub_order_status'] != 1) $this->throwError($val . '当前订单状态不是已支付');
                $where[] = ['id' => $params['rebind_package_id']];
                $es      = new ElasticSearchService();
                $arr     = array(
                    'index'  => ['periods_set'],
                    'match'  => $where,
                    'source' => ['id', 'period_id', 'periods_type', 'force_original_package', 'associated_products'],
                    'limit'  => 1
                );
                $data    = $es->getDocumentList($arr);
                if (!isset($data['data'][0])) $this->throwError($val . '未获取到重绑套餐信息');
                $rebindPackageInfo = $data['data'][0];
                if ($rebindPackageInfo['period_id'] != $params['rebind_period']) $this->throwError($val . '重绑期数与套餐信息不匹配');
                if ($rebindPackageInfo['periods_type'] != $params['order_type']) $this->throwError($val . '重绑商品频道与订单类型不匹配');
                if ($params['rebind_period'] == $orderInfo['period'] && $params['rebind_package_id'] == $orderInfo['package_id']) $this->throwError($val . '重绑套餐与原套餐相同，请核对再提交');
                $updateData = array(
                    'period'      => $params['rebind_period'],
                    'package_id'  => $params['rebind_package_id'],
                    'update_time' => time()
                );
                if (isset($params['rebind_order_qty'])) $updateData['order_qty'] = $params['rebind_order_qty'];
                if (isset($rebindPackageInfo['force_original_package'])) {
                    if ($rebindPackageInfo['force_original_package'] == 1) {
                        $updateData['is_original_package'] = 1;
                        $updateData['express_type']        = 4;
                    } else {
                        $updateData['is_original_package'] = 0;
                    }
                }
                $updateOrder = Db::name($order_type[$params['order_type']]['table'])->where(['sub_order_no' => $val, 'order_type' => $params['order_type']])->update($updateData);
                if (empty($updateOrder)) $this->throwError($val . '重绑套餐失败');
                //添加订单备注
                $remarks = array(
                    'sub_order_no' => $val,
                    'order_type'   => $params['order_type'],
                    'content'      => $params['remark'] . $orderInfo['period'] . '-' . $orderInfo['package_id'] . '-->' . $params['rebind_period'] . '-' . $params['rebind_package_id'],
                    'admin_id'     => $params['admin_id']
                );
                $this->createRemarks($remarks);
                if ($orderInfo['push_wms_status'] == 1) {
                    $associated_products = json_decode($rebindPackageInfo['associated_products'], true);
                    $product             = [];
                    foreach ($associated_products as &$v) {
                        $productInfo = Db::table('vh_wiki.vh_products')->field('bar_code,short_code,grape_picking_years,capacity')->where(['id' => $v['product_id']])->find();
                        if (isset($params['rebind_order_qty'])) {
                            $order_qty = $params['rebind_order_qty'];
                        } else {
                            $order_qty = $orderInfo['order_qty'];
                        }
                        $number    = $order_qty * $v['nums'];
                        $product[] = array(
                            'goods_id'    => $rebindPackageInfo['period_id'],
                            'goods_code'  => $productInfo['bar_code'],
                            'number'      => $number,
                            'short_code'  => $productInfo['short_code'],
                            'goods_years' => $productInfo['grape_picking_years'],
                            'volume'      => $productInfo['capacity'],
                        );
                    }
                    $push_wms_data               = [];
                    $push_wms_data['store_code'] = env('ORDERS.STORE_CODE');
                    $push_wms_data['orderno']    = $params['sub_order_no'];
                    $push_wms_data['product']    = $product;
                    if (isset($updateData['express_type'])) $push_wms_data['logistics_id'] = $updateData['express_type'];
                    if (isset($updateData['is_original_package'])) $push_wms_data['is_originalbox'] = $updateData['is_original_package'];
                    $syncRes = $this->httpPost(env("ITEM.DISTRIBUTE_URL") . "/sync/shiporder/receiptInfo", $push_wms_data, ["Content-Type" => "application/json"]);
                    if (isset($syncRes['status'])) {
                        if ($syncRes['status'] == 'fail') {
                            $this->throwError('同步萌牙发货单信息异常：' . $syncRes['msg']);
                        }
                    } else {
                        $this->throwError('同步萌牙发货单信息异常：萌牙模块访问异常');
                    }
                } elseif ($orderInfo['push_wms_status'] == 2  && $params['order_type'] != 2) { //推送失败重推WMS
                    $pushData = [
                        'exchange_name' => 'orders',
                        'routing_key'   => 'push.wms',
                        'data'          => base64_encode(json_encode([
                            'sub_order_no' => $val,
                            'order_type'   => $params['order_type']
                        ])),
                    ];
                    Log::write("换绑套餐重推WMS队列 1: " . json_encode($pushData));
                    httpPostString(env('ITEM.QUEUE_URL'), json_encode($pushData));
                }
            }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $this->throwError($e->getMessage());
        }
        return true;
    }

    /**
     * Description:获取拼团信息
     * Author: zrc
     * Date: 2022/4/2
     * Time: 10:17
     * @param $requestparams
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getGroupInfo($requestparams)
    {
        $params    = $requestparams;
        $groupInfo = Db::name('order_group')->field('id,period,package_id,group_status,group_limit_nums,group_join_nums,group_status,over_time,complete_time,head_uid,member_uids')->where(['id' => $params['group_id']])->find();
        if (empty($groupInfo)) $this->throwError('未获取到拼团信息');
        //剩余拼团时间
        $remaining_time = $groupInfo['over_time'] - time() <= 0 ? 0 : $groupInfo['over_time'] - time();
        //拼团剩余人数
        $group_last_num = $groupInfo['group_limit_nums'] - $groupInfo['group_join_nums'] <= 0 ? 0 : $groupInfo['group_limit_nums'] - $groupInfo['group_join_nums'];
        //参与拼团的用户信息
        $user_head_img = [];
        $user_id_str   = $groupInfo['head_uid'];
        $userInfo      = httpGet(env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo', ['uid' => $user_id_str, 'info_type' => 1]);
        if ($userInfo['error_code'] == 0 && isset($userInfo['data']['list'][0]['avatar_image'])) {
            $user_head_img[] = imagePrefix($userInfo['data']['list'][0]['avatar_image']);
        }
        if (!empty($groupInfo['member_uids'])) {
            $userInfo = httpGet(env('ITEM.USER_URL') . '/user/v3/profile/getUserInfo', ['uid' => $groupInfo['member_uids'], 'info_type' => 1]);
            if ($userInfo['error_code'] == 0 && count($userInfo['data']['list']) > 0) {
                foreach ($userInfo['data']['list'] as &$val) {
                    $user_head_img[] = imagePrefix($val['avatar_image']);
                }
            }
        }
        $result = array(
            'group_id'        => $groupInfo['id'],
            'group_status'    => $groupInfo['group_status'],
            'remaining_time'  => $remaining_time,
            'group_last_num'  => $group_last_num,
            'user_head_img'   => $user_head_img,
            'group_share_url' => env('ORDERS.group_share_url') . '拼团信息地址待完善'
        );
        return $result;
    }

    /**
     * Description:订单统计待支付数、已支付数、已发货数、待拼单数
     * Author: zrc
     * Date: 2022/4/2
     * Time: 16:57
     * @param $requestparams
     * @return array
     */
    public function orderStatistics($requestparams)
    {
        $params = $requestparams;
        $es     = new ElasticSearchService();
        //待支付数
        $unpaid_nums = Db::name('order_main')->where(['uid' => $params['uid'], 'main_order_status' => 0])->count();
        //已支付数
        $data = $es->getDocumentList(['index' => ['orders'], 'match' => [['uid' => $params['uid']], ['sub_order_status' => 1], ['group_status' => 0], ['refund_status' => 0], ['is_after_sale' => 0], ['is_delete' => 0]], 'source' => ['sub_order_no']]);
        //兔头
        $rabbitData = $es->getDocumentList(['index' => ['orders'], 'match' => [['uid' => $params['uid']], ['sub_order_status' => 1], ['order_type' => 4]], 'source' => ['sub_order_no']]);
        //跨境
        $crossData = $es->getDocumentList(['index' => ['orders'], 'match' => [['uid' => $params['uid']], ['sub_order_status' => 1], ['order_type' => 2], ['refund_status' => 0]], 'source' => ['sub_order_no']]);
        $paid_nums = $data['total']['value'] + $rabbitData['total']['value'] + $crossData['total']['value'];
        //已发货数
        $data = $es->getDocumentList(['index' => ['orders'], 'match' => [['uid' => $params['uid']], ['sub_order_status' => 2], ['is_after_sale' => 0]], 'source' => ['sub_order_no']]);
        //兔头
        $rabbitData = $es->getDocumentList(['index' => ['orders'], 'match' => [['uid' => $params['uid']], ['sub_order_status' => 2], ['order_type' => 4]], 'source' => ['sub_order_no']]);
        //跨境
        $crossData    = $es->getDocumentList(['index' => ['orders'], 'match' => [['uid' => $params['uid']], ['sub_order_status' => 2], ['order_type' => 2], ['is_after_sale' => 0]], 'source' => ['sub_order_no']]);
        $shipped_nums = $data['total']['value'] + $rabbitData['total']['value'] + $crossData['total']['value'];
        //待拼单数
        $arr           = array(
            'index'  => ['orders'],
            'match'  => [['uid' => $params['uid']], ['sub_order_status' => 1], ['group_status' => 1]],
            'source' => ['sub_order_no']
        );
        $data          = $es->getDocumentList($arr);
        $unporder_nums = $data['total']['value'];
        //售后订单数
        $arr             = array(
            'index'  => ['orders'],
            'match'  => [['uid' => $params['uid']], ['is_after_sale' => 1], ['work_order_status' => 1], ['is_delete' => 0]],
            'source' => ['sub_order_no']
        );
        $data            = $es->getDocumentList($arr);
        $after_sale_nums = $data['total']['value'];
        $result          = array(
            'unpaid_nums'     => $unpaid_nums,
            'paid_nums'       => $paid_nums,
            'shipped_nums'    => $shipped_nums,
            'unporder_nums'   => $unporder_nums,
            'after_sale_nums' => $after_sale_nums,
        );
        return $result;
    }

    /**
     * Description:订单自动退款
     * Author: zrc
     * Date: 2022/4/6
     * Time: 12:30
     * @param $requestparams
     * @return bool
     * @throws \Exception
     */
    public function orderAutomaticRefund($requestparams)
    {
        $params = $requestparams;
        //查询订单信息
        $orderInfo = $this->orderDetail(['order_no' => $params['sub_order_no'], 'field' => 'id,uid,bonus_balance,recharge_balance,cash_amount,main_order_no,payment_method,1,order_type,payment_subject']);
        try {
            //生成退款订单
            $refund_order_no = creatOrderNo(env('ORDERS.REFUND'), $orderInfo['uid']);
            $insertData      = array(
                'refund_order_no' => $refund_order_no,
                'main_order_no'   => $orderInfo['main_order_no'],
                'refund_status'   => 0,
                'sub_order_no'    => $params['sub_order_no'],
                'payment_method'  => $orderInfo['payment_method'],
                'payment_subject' => $orderInfo['payment_subject'],
                'refund_amount'   => isset($params['refund_money']) ? $params['refund_money'] : $orderInfo['payment_amount'],
                'created_time'    => time(),
            );
            $addRefund       = Db::name('refund_order')->insert($insertData);
            if (empty($addRefund)) $this->throwError('生成退款订单失败');
            //发起银联退款
            $pushData    = array(
                'main_order_no'   => $orderInfo['payment_method'] . $orderInfo['main_order_no'],//支付方式+主订单号为银联交易订单号
                'payment_method'  => $orderInfo['payment_method'],
                'refund_amount'   => isset($params['refund_money']) ? $params['refund_money'] : $orderInfo['payment_amount'],
                'refund_order_no' => $refund_order_no,
                'subject'         => $orderInfo['payment_subject'],
                'is_cross'        => $orderInfo['order_type'] == 2 ? 1 : 0,
                'order_type'      => $orderInfo['order_type'],
                'sub_order_no'    => $params['sub_order_no']
            );
            $orderRefund = $this->httpPost(env('ITEM.PAYMENT_URL') . '/payment/v3/ums/refund', $pushData);
            if (!isset($orderRefund['error_code']) || $orderRefund['error_code'] != 0) {
                Db::name('refund_order')->where(['refund_order_no' => $refund_order_no, 'main_order_no' => $orderInfo['main_order_no'], 'sub_order_no' => $params['sub_order_no']])->update(['refund_status' => 2, 'update_time' => time()]);
                $this->throwError($orderRefund['error_msg']);
            }
            //修改退款订单状态
            $updateRefund = Db::name('refund_order')->where(['refund_order_no' => $refund_order_no, 'main_order_no' => $orderInfo['main_order_no'], 'sub_order_no' => $params['sub_order_no']])->update(['refund_status' => 1, 'update_time' => time()]);
            if (empty($updateRefund)) $this->throwError('修改退款订单失败');
        } catch (\Exception $e) {
            $this->throwError($e->getMessage());
        }
        return true;
    }

    /**
     * Description:获取订单盲盒随机信息
     * Author: zrc
     * Date: 2022/5/26
     * Time: 14:21
     * @param $requestparams
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getMysteryBoxLog($params)
    {
        $data = Db::name('order_mystery_box_log')
            ->alias('mb')
            ->field('mb.*')
            ->leftJoin('order_main om', 'om.main_order_no=mb.main_order_no')
            ->where([['mb.package_id', '=', $params['package_id']], ['om.main_order_status', 'in', [1, 2, 3]]])
            ->select()->toArray();
        return ['list' => $data];
    }

    /**
     * Description:订金订单列表
     * Author: zrc
     * Date: 2023/6/21
     * Time: 13:34
     * @param $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function depositList($params)
    {
        $result     = [];
        $list       = [];
        $offset     = ($params['page'] - 1) * $params['limit'];
        $where      = [];
        $where[]    = ['uid', '=', $params['uid']];
        $where[]    = ['special_type', '=', 4];
        $where[]    = ['is_delete', '=', 0];
        $where[]    = ['sub_order_status', '<>', 0];
        $orderLists = Db::name('flash_order')->where($where)->limit($offset, $params['limit'])->order('created_time desc')->select()->toArray();
        $total      = Db::name('flash_order')->where($where)->limit($offset, $params['limit'])->count();
        if (count($orderLists) > 0) {
            $sub_order_no_arr = array_column($orderLists, 'sub_order_no');
            $es               = new ElasticSearchService();
            $terms[]          = ['sub_order_no.keyword' => $sub_order_no_arr];
            $arr              = array(
                'index' => ['orders'],
                'terms' => $terms,
                'limit' => 100,
                'sort'  => [['created_time' => 'desc']]
            );
            $esData           = $es->getDocumentList($arr);
            if (isset($esData['data'][0])) {
                foreach ($esData['data'] as &$vv) {
                    $orderData = $vv;
                    //数据库查询数据覆盖es查询数据
                    foreach ($orderLists as &$vvv) {
                        if ($vvv['sub_order_no'] == $orderData['sub_order_no']) {
                            $orderData['sub_order_status'] = $vvv['sub_order_status'];
                            $orderData['refund_status']    = isset($vvv['refund_status']) ? $vvv['refund_status'] : 0;
                        }
                    }
                    //订单状态处理 订单状态 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消 5-拼团中 6-已暂存 7-退款中 8-退款成功
                    if (isset($orderData['refund_status']) && $orderData['refund_status'] == 1) $orderData['sub_order_status'] = 7;
                    if (isset($orderData['refund_status']) && $orderData['refund_status'] == 2) $orderData['sub_order_status'] = 8;
                    //获取商品信息处理列表尾款信息
                    $esPeriodsData = $es->getDocumentList(['index' => ['periods'], 'match' => [['id' => $orderData['period']]], 'source' => ['sell_time'], 'limit' => 1]);
                    if (isset($esPeriodsData['data'][0]['sell_time'])) {
                        $countdown = strtotime($esPeriodsData['data'][0]['sell_time']) - time();
                        if ($countdown < 0) $countdown = 0;
                    }
                    $esPeriodsSetData     = $es->getDocumentList(['index' => ['periods_set'], 'match' => [['id' => $orderData['package_id']]], 'source' => ['price', 'deposit_coupon_value'], 'limit' => 1]);
                    $deposit_coupon_value = isset($esPeriodsSetData['data'][0]['deposit_coupon_value']) ? $esPeriodsSetData['data'][0]['deposit_coupon_value'] : 0;
                    $balance_payment      = isset($esPeriodsSetData['data'][0]['price']) ? $esPeriodsSetData['data'][0]['price'] - $esPeriodsSetData['data'][0]['deposit_coupon_value'] : 0;
                    $list[]               = [
                        'order_no'             => $orderData['sub_order_no'],
                        'status'               => $orderData['sub_order_status'],
                        'special_type'         => $orderData['special_type'],
                        'goodsInfo'            => [[
                            'period'        => $orderData['period'],
                            'package_id'    => $orderData['package_id'],
                            'goods_img'     => imagePrefix($orderData['banner_img']),
                            'goods_title'   => $orderData['title'],
                            'package_name'  => $orderData['package_name'],
                            'order_qty'     => $orderData['order_qty'],
                            'package_price' => isset($orderData['special_price']) ? $orderData['special_price'] : $orderData['package_price'],
                            'periods_type'  => $orderData['order_type'],
                            'label'         => isset($orderData['delivery_method']) ? $orderData['delivery_method'] == 1 ? 2 : 1 : 0
                        ]],
                        'total_qty'            => $orderData['order_qty'],
                        'payment_amount'       => $orderData['order_type'] == 4 ? $orderData['rabbit_payment_amount'] : $orderData['payment_amount'],
                        'created_time'         => $orderData['created_time'],
                        'predict_time'         => $orderData['predict_time'],
                        'order_type'           => $orderData['order_type'],
                        'countdown'            => isset($countdown) ? $countdown : 0,
                        'deposit_coupon_value' => $deposit_coupon_value,
                        'balance_payment'      => $balance_payment > 0 ? $balance_payment : 0.01,
                    ];
                }
                $result['list']  = $list;
                $result['total'] = $total;
            }
        }
        return $result;
    }

    /**
     * Description:订金订单详情
     * Author: zrc
     * Date: 2023/6/21
     * Time: 18:08
     * @param $params
     * @return array
     * @throws \Exception
     */
    public function depositDetail($params)
    {
        $es   = new ElasticSearchService();
        $arr  = array(
            'index' => ['orders'],
            'match' => [['sub_order_no.keyword' => $params['order_no']], ['uid' => $params['uid']]],
            'limit' => 1
        );
        $data = $es->getDocumentList($arr);
        if (!isset($data['data'][0])) $this->throwError('未获取到订单详情，请稍后重试');
        $encrypt         = cryptionDeal(2, [$data['data'][0]['consignee'], $data['data'][0]['consignee_phone']], $params['uid'], '前端用户');
        $consignee       = isset($encrypt[$data['data'][0]['consignee']]) ? $encrypt[$data['data'][0]['consignee']] : '';
        $consignee_phone = isset($encrypt[$data['data'][0]['consignee_phone']]) ? $encrypt[$data['data'][0]['consignee_phone']] : '';
        //订单状态处理 订单状态 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消 7-退款中 8-退款成功
        $status = $data['data'][0]['sub_order_status'];
        if (isset($data['data'][0]['refund_status']) && $data['data'][0]['refund_status'] == 1) $status = 7;
        if (isset($data['data'][0]['refund_status']) && $data['data'][0]['refund_status'] == 2) $status = 8;
        //获取商品信息处理列表尾款信息
        $esPeriodsData = $es->getDocumentList(['index' => ['periods'], 'match' => [['id' => $data['data'][0]['period']]], 'source' => ['sell_time'], 'limit' => 1]);
        $countdown     = 0;
        if (isset($esPeriodsData['data'][0]['sell_time'])) {
            $countdown = strtotime($esPeriodsData['data'][0]['sell_time']) - time();
        }
        $esPeriodsSetData     = $es->getDocumentList(['index' => ['periods_set'], 'match' => [['id' => $data['data'][0]['package_id']]], 'source' => ['price', 'deposit_coupon_value'], 'limit' => 1]);
        $deposit_coupon_value = isset($esPeriodsSetData['data'][0]['deposit_coupon_value']) ? $esPeriodsSetData['data'][0]['deposit_coupon_value'] : 0;
        $balance_payment      = isset($esPeriodsSetData['data'][0]['price']) ? round($esPeriodsSetData['data'][0]['price'] - $esPeriodsSetData['data'][0]['deposit_coupon_value'], 2) : 0;
        $result               = array(
            'order_no'             => $params['order_no'],
            'status'               => $status,
            'payment_method'       => $data['data'][0]['payment_method'],
            'payment_amount'       => $data['data'][0]['order_type'] == 4 ? $data['data'][0]['rabbit_payment_amount'] : $data['data'][0]['payment_amount'],
            'created_time'         => $data['data'][0]['created_time'],
            'countdown'            => $countdown <= 0 ? 0 : $countdown,
            'province_id'          => $data['data'][0]['province_id'],
            'city_id'              => $data['data'][0]['city_id'],
            'district_id'          => $data['data'][0]['district_id'],
            'province_name'        => $data['data'][0]['province_name'],
            'city_name'            => $data['data'][0]['city_name'],
            'district_name'        => $data['data'][0]['district_name'],
            'address'              => $data['data'][0]['address'],
            'goods_price'          => $data['data'][0]['package_price'] * $data['data'][0]['order_qty'],
            'total_qty'            => $data['data'][0]['order_qty'],
            'consignee'            => $consignee,
            'consignee_phone'      => $consignee_phone,
            'refund_status'        => isset($data['data'][0]['refund_status']) ? $data['data'][0]['refund_status'] : 0,
            'refund_start_time'    => isset($data['data'][0]['refund_start_time']) ? $data['data'][0]['refund_start_time'] : '',
            'refund_end_time'      => isset($data['data'][0]['refund_end_time']) ? $data['data'][0]['refund_end_time'] : '',
            'refund_reject_time'   => isset($data['data'][0]['refund_reject_time']) ? $data['data'][0]['refund_reject_time'] : '',
            'payment_time'         => $data['data'][0]['payment_time'],
            'order_type'           => $data['data'][0]['order_type'],
            'deposit_coupon_value' => $deposit_coupon_value,
            'balance_payment'      => $balance_payment > 0 ? $balance_payment : 0.01,
            'goodsInfo'            => array(
                array(
                    'period'        => $data['data'][0]['period'],
                    'package_id'    => $data['data'][0]['package_id'],
                    'goods_img'     => imagePrefix($data['data'][0]['banner_img']),
                    'goods_title'   => $data['data'][0]['title'],
                    'package_name'  => $data['data'][0]['package_name'],
                    'order_qty'     => $data['data'][0]['order_qty'],
                    'package_price' => isset($data['data'][0]['special_price']) && $data['data'][0]['special_price'] > 0 ? $data['data'][0]['special_price'] : $data['data'][0]['package_price'],
                    'periods_type'  => $data['data'][0]['order_type'],
                )
            )
        );
        if (in_array($result['payment_method'], [0, 1, 6])) {
            $payment_method_name = '支付宝';
        } else if (in_array($result['payment_method'], [3, 4, 5, 7, 8])) {
            $payment_method_name = '微信';
        } else if ($result['payment_method'] == 2) {
            $payment_method_name = '扫码支付';
        } else {
            $config_payment_method = config('config')['payment_method'];//订单支付方式获取
            $payment_method_name   = isset($config_payment_method[intval($result['payment_method']) + 1]['label']) ? $config_payment_method[intval($result['payment_method'] + 1)]['label'] : '';
        }
        $result['payment_name'] = $payment_method_name;
        return $result;
    }

    /**
     * Description:获取用户首三单订单个数
     * Author: zrc
     * Date: 2023/6/26
     * Time: 13:14
     * @param $params
     * @return int[]
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getUserTopThreeOrderNums($params)
    {
        $result = array(
            'is_show'      => 0,
            'coupon_money' => 0,
            'stage'        => 0,
        );
        $redis  = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        $redis->auth(env('CACHE.PASSWORD'));
        $redis->select(8);
        $redisCache = $redis->hGet('userIsHaveTopThree', $params['uid']);
        if ($redisCache != 1) {
            $where         = array(
                ['uid', '=', $params['uid']],
                ['payment_time', '>', 0],
            );
            $topThreeOrder = Db::name('order_main')->where($where)->order('id asc')->count();
            if ($topThreeOrder <= 3) {
                switch ($topThreeOrder) {
                    case 0:
                        $stage = 1;
                        break;
                    case 1:
                        $stage = 1;
                        break;
                    case 2:
                        $stage = 2;
                        break;
                    case 3:
                        $stage = 3;
                        break;
                }
                $result['stage'] = $stage;
                //获取规则
                $discountInfo = Db::table('vh_marketing.vh_top_three_order_discount')->where(['stage' => $stage, 'status' => 1, 'type' => 1])->find();
                if (!empty($discountInfo)) {
                    $result['is_show']      = 1;
                    $coupon_face_value      = Db::table('vh_marketing.vh_coupon')->where(['id' => $discountInfo['data_id']])->value('coupon_face_value');
                    $result['coupon_money'] = intval($coupon_face_value);
                }
            }
        }
        return $result;
    }

    /**
     * Description:用户暂存订单
     * Author: zrc
     * Date: 2023/9/6
     * Time: 16:59
     * @param $params
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function stagingOrder($params)
    {
        //查询订单信息
        $searchData  = array(
            'sub_order_no' => $params['sub_order_no'],
            'fields'       => 'uid,order_type,sub_order_status,refund_status,freeze_status,warehouse_code,push_wms_status,payment_doc,express_type,is_ts'
        );
        $batchSearch = curlRequest(env('ITEM.MYSQL_BATCH_SEARCH') . '/services/v3/batchsearch/orders/fields', $searchData, [], 'GET');
        if (!isset($batchSearch['error_code']) || $batchSearch['error_code'] != 0) $this->throwError('获取订单信息异常，请稍后重试');
        $orderInfo = $batchSearch['data'];
        if (empty($orderInfo)) $this->throwError('未获取到订单信息');
        if ($orderInfo['uid'] != $params['uid']) $this->throwError('未获取到订单信息');
        if ($orderInfo['express_type'] == 3) $this->throwError('您的订单属于冷链，无法同时进行暂存处理');
        if ($orderInfo['is_ts'] == 1) $this->throwError('订单已暂存，请勿重复操作');
        if ($orderInfo['sub_order_status'] != 1) $this->throwError('当前订单状态无法进行暂存');
        if ($orderInfo['order_type'] == 2) {
            if ($orderInfo['payment_doc'] == 1) $this->throwError('订单报关中不支持自动暂存，请联系客服处理');
        }
        //获取仓库编码配置
        $config_warehouse = config('config')['warehouse'];
        $wms_id           = 1;
        foreach ($config_warehouse as &$val) {
            if (in_array($orderInfo['warehouse_code'], explode(',', $val['code']))) $wms_id = $val['value'];
        }
        if ($wms_id == 2) $this->throwError('当前订单仓库不支持自动暂存，请联系客服处理');
        Db::startTrans();
        try {
            $this->updateOrder(['order_no' => $params['sub_order_no'], 'order_type' => $params['order_type'], 'is_ts' => 1, 'operator' => '-1']);

            $params['status'] = 1;
            $p_ts_time = empty($params['ts_time']) ? 0 : strtotime($params['ts_time']);
            $ts_log    = Db::table('vh_orders.vh_order_ts')->where('sub_order_no', $params['sub_order_no'])->find() ?? [];
            $ts_update = [
                'sub_order_no' => $params['sub_order_no'],
                'order_type'   => $params['order_type'],
                'ts_time'      => $p_ts_time,
                'admin_id'     => $params['admin_id'] ?? null,
                'uid'          => $ts_log['uid'] ?? null,
                'remarks'      => ($ts_log['remarks'] ?? '') . " 用户设置:暂存",
                'created_time' => $ts_log['created_time'] ?? time(),
                'update_time'  => time(),
                'status'       => $params['status'] == 1 ? 0 : 3,
            ];
            if (empty($ts_log)) {
                Db::table('vh_orders.vh_order_ts')->insert($ts_update);
            } else {
                Db::table('vh_orders.vh_order_ts')->where('sub_order_no', $params['sub_order_no'])->update($ts_update);
            }
            Db::commit();
        } catch (\Exception $e) {
            //var_dump($e->getMessage());
            Db::rollback();
            $this->throwError($e->getMessage());
        }
        return true;
    }

    /**
     * Description:用户开启冷链配送创建订单
     * Author: zrc
     * Date: 2023/9/7
     * Time: 16:35
     * @param $params
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function upgradeColdChain($params)
    {
        //查询订单信息
        $searchData  = array(
            'sub_order_no' => $params['sub_order_no'],
            'fields'       => 'uid,order_type,sub_order_status,refund_status,freeze_status,warehouse_code,push_wms_status,payment_doc,express_type,is_ts,is_original_package,express_fee,package_id,order_qty,main_order_id'
        );
        $batchSearch = curlRequest(env('ITEM.MYSQL_BATCH_SEARCH') . '/services/v3/batchsearch/orders/fields', $searchData, [], 'GET');
        if (!isset($batchSearch['error_code']) || $batchSearch['error_code'] != 0) $this->throwError('获取订单信息异常，请稍后重试');
        $orderInfo = $batchSearch['data'];
        if (empty($orderInfo)) $this->throwError('未获取到订单信息');
        if ($orderInfo['uid'] != $params['uid']) $this->throwError('未获取到订单信息');
        $orderMainInfo            = Db::name('order_main')->field('province_id,city_id')->where(['id' => $orderInfo['main_order_id']])->find();
        $orderInfo['province_id'] = $orderMainInfo['province_id'];
        $orderInfo['city_id']     = $orderMainInfo['city_id'];

        $receive_information = Db::name('sub_order_receive_information')->field('province_id,city_id')->where(['sub_order_no' => $params['sub_order_no']])->order('id desc')->find();
        if (!empty($receive_information)) {
            $orderInfo['province_id'] = $receive_information['province_id'];
            $orderInfo['city_id']     = $receive_information['city_id'];
        }

        if ($orderInfo['is_ts'] == 1) $this->throwError('请先取消暂存再开启温控包裹');
        if (in_array($orderInfo['express_type'], [3, 31])) $this->throwError('订单已是温控包裹运输，请勿重复操作');
        if ($orderInfo['sub_order_status'] != 1) $this->throwError('当前订单状态无法开启温控包裹');
        if ($orderInfo['order_type'] == 2) {
            $cross_courier_fee = Db::name('cross_sfll_area')->where(['regional_id' => $orderInfo['province_id']])->value('courier_fee');
            if (empty($cross_courier_fee)) {
                $this->throwError('当前地区暂不支持温控包裹。');
            }
            if ($orderInfo['payment_doc'] == 1) $this->throwError('订单报关中不支持自动开启温控包裹，请联系客服处理');
        }
        if ($orderInfo['is_original_package'] == 1) $this->throwError('原箱不支持温控包裹运输');
        $month = date('m');
        if (5 > $month || $month > 10) $this->throwError('当前月份不支持温控包裹运输');
        //获取仓库编码配置
        $config_warehouse = config('config')['warehouse'];
        $wms_id           = 1;
        foreach ($config_warehouse as &$val) {
            if (in_array($orderInfo['warehouse_code'], explode(',', $val['code']))) $wms_id = $val['value'];
        }
        if ($wms_id == 2) $this->throwError('当前订单仓库不支持自动开启温控包裹，请联系客服处理');
        //查询萌牙订单状态
        $wmsStatus = curlRequest(env('ITEM.DISTRIBUTE_URL') . '/query/OrderSpecifyFields', json_encode(['store_code' => env('ORDERS.STORE_CODE'), 'orderno' => $params['sub_order_no'], 'field' => 'is_cancel_order']));
        if (isset($wmsStatus['data']['list'][0]['is_cancel_order']) && $wmsStatus['data']['list'][0]['is_cancel_order'] != 0) throw new \Exception('订单已拣货，请联系客服处理！', 501); //$this->throwError('开启温控包裹失败，请联系客服处理');
        $result = [];
        Db::startTrans();
        try {
            $afterSalesService = new AfterSalesService();
            //冻结订单
            if ($orderInfo['freeze_status'] == 0) {
                $freezeData = array(
                    'sub_order_no' => $params['sub_order_no'],
                    'order_type'   => $params['order_type'],
                    'type'         => 1,
                );
                $afterSalesService->freezeOrder($freezeData);
            }
            //冷链费用计算
            $difference = 9999;
            if ($orderInfo['order_type'] == 2 && !empty($cross_courier_fee)) {//跨境商品冷链费计算
                $difference  = round($cross_courier_fee - $orderInfo['express_fee'], 2);
            } else {//普通商品冷链费计算
                //套餐ID获取商品套餐信息
                $packageInfo         = esGetOne($orderInfo['package_id'], 'vinehoo.periods_set');
                $associated_products = json_decode($packageInfo['associated_products'], true);
                $capacity            = 0;
                foreach ($associated_products as &$vv) {
                    $productsCapacity = Db::table('vh_wiki.vh_products')->where(['id' => $vv['product_id']])->value('capacity');
                    $capacity         += (int)$productsCapacity * $vv['nums'] * $orderInfo['order_qty'];//套餐的总容量
                }
                $freight    = calcFreight($orderInfo['city_id'], $capacity);
                $difference = round($freight - $orderInfo['express_fee'], 2);
            }
            //创建补差价订单
            $createData = array(
                'admin_id'           => 0,
                'sub_order_no'       => $params['sub_order_no'],
                'order_type'         => $params['order_type'],
                'type'               => 1,
                'express_type'       => 31,
                'freight_difference' => $difference,
            );
            $result     = $afterSalesService->manualCreateOrder($createData);
            Db::commit();
            return $result;
        } catch (\Exception $e) {
            Db::rollback();
            //解冻订单
            $freezeData = array(
                'sub_order_no' => $params['sub_order_no'],
                'order_type'   => $params['order_type'],
                'type'         => 2,
            );
            curlRequest(env('ITEM.ORDERS_URL') . '/orders/v3/afterSales/freezeOrder', $freezeData);
            $msg = $e->getCode() == 501 ? $e->getMessage() : '开启温控包裹失败，请联系客服处理';
            $this->throwError($msg);
        }
        return $result;
    }


    public function applyBilling($params)
    {
        if ($params['order_type'] == 0) {
            $model = new \app\model\Flash(); //闪购
        } else if ($params['order_type'] == 1) {
            $model = new \app\model\Second(); //秒发
        } else if ($params['order_type'] == 2) {
            $model = new \app\model\Cross();
        } else if ($params['order_type'] == 3) {
            $model = new \app\model\Tail();
        } else if ($params['order_type'] == 9) {
            $model = new \app\model\MerchantSecon();
        }

        $order = $model->where('uid', $params['uid'])->where('sub_order_no', $params['sub_order_no'])->find();

        if (!in_array($order['sub_order_status'], [1, 2])) {
            throw new Exception("订单状态为待发货、已暂存、待收货才能申请开票!");
        }
        if (!empty($order['invoice_progress'])) {
            throw new Exception("请勿重复申请开票。");
        }

        //获取发票抬头信息
        $receipt = curlRequest(env('ITEM.USER_URL') . '/user/v3/receipt/queryInfo', ['invoice_id' => $params['invoice_id']], [], 'GET');
        if (!isset($receipt['data']['list'][0]['invoice_name'])) $this->throwError('获取发票抬头信息失败');
        $invoiceInfo         = $receipt['data']['list'][0];
        $invoiceData         = array(
            'sub_order_no'    => $order['sub_order_no'],
            'order_type'      => $order['order_type'],
            'invoice_type'    => $invoiceInfo['invoice_type'],
            'invoice_name'    => $invoiceInfo['invoice_name'] ?? '',
            'type_id'         => $invoiceInfo['type_id'],
            'email'           => $invoiceInfo['email'] ?? '',
            'taxpayer'        => $invoiceInfo['taxpayer'] ?? '',
            'telephone'       => $invoiceInfo['telephone'] ?? '',
            'company_address' => $invoiceInfo['company_address'] ?? '',
            'company_tel'     => $invoiceInfo['company_tel'] ?? '',
            'opening_bank'    => $invoiceInfo['opening_bank'] ?? '',
            'bank_account'    => $invoiceInfo['bank_account'] ?? '',
        );
        $orderInvoiceService = new OrderInvoiceService();
        $invoice_id          = $orderInvoiceService->addInvoiceRecord($invoiceData);

        $order->invoice_progress = 1;
        $order->invoice_id       = $params['invoice_id'];
        $order->save();

        return true;
    }

    public function notCancelled($params)
    {
        $err_orders = [];
        $agentid    = "80e700e4-bb46-496c-a5e5-1a41714c77df";

        $order_types = [0, 1, 2, 3, 9];
        foreach ($order_types as $order_type) {

            $where = [
                ['t1.sub_order_status', '=', 0],
                ['t1.is_delete', '=', 0],
                ['t1.payment_amount', '>', 0],
                ['t1.created_time', '>', strtotime('-30 days')],
                ['t1.created_time', '<', strtotime('-61 min')],
            ];

            if ($order_type == 0) {
                $model = new \app\model\Flash(); //闪购
                $where[] =  ['t1.special_type', '=', 0]; //特殊类型：0-普通 1-拼团 2-新人 4-定金
            } else if ($order_type == 1) {
                $model = new \app\model\Second(); //秒发
                $where[] =  ['t1.special_type', '=', 0]; //特殊类型：0-普通 1-拼团 2-新人 4-定金
            } else if ($order_type == 2) {
                $model = new \app\model\Cross();
            } else if ($order_type == 3) {
                $model = new \app\model\Tail();
                $where[] =  ['t1.special_type', '=', 0]; //特殊类型：0-普通 1-拼团 2-新人 4-定金
            } else if ($order_type == 9) {
                $model = new \app\model\MerchantSecon();
                $where[] =  ['t1.special_type', '=', 0]; //特殊类型：0-普通 1-拼团 2-新人 4-定金
            }

            $list = $model->alias('t1')
                ->join('order_main t2', 't1.main_order_id = t2.id')
                ->where($where)->column('t1.sub_order_no,t1.created_time,t2.payment_method');

            foreach ($list as $order) {
                if (in_array($order['payment_method'], [-1, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9])) {
                    $err_orders[] = $order['sub_order_no'];
                } elseif (in_array($order['payment_method'], [10, 11]) && ((time() - $order['created_time']) > env('ORDERS.transfer_pay_time_out'))) {
                    $err_orders[] = $order['sub_order_no'];
                }
            }
        }
        if(!empty($err_orders)){
            $res = \Curl::sendWechatSender([
                'msg'          => "超时未取消订单: " . implode(',', $err_orders),
                'at'           => '17772336502',
                'access_token' => $agentid,
            ]);
        }
        return true;
    }

    /**
     * @方法描述: 取消暂存发货
     * <AUTHOR>
     * @Date 2025/6/16 16:41
     * @param $params
     * @return true|void
     * @throws \think\db\exception\DbException
     */
    public function cancelTs($params)
    {
        $list = Db::name('order_ts')
            ->where('order_type', 'in', [0, 1, 2, 3, 7]) //闪购秒发跨境尾货
            ->whereDay('ts_time') //暂存到期时间
            ->where('status', 'in', [0, 2]) //状态:0=待执行,1=执行成功,2=执行失败,3=取消执行
            ->column('*');

        foreach ($list as $item) {
            $model = $model2 = null;
            Db::startTrans();
            try {
                $field = 't2.main_order_status,t1.predict_time,t2.main_order_no,t1.sub_order_status,t1.sub_order_no,t1.is_delete,t1.is_ts,t1.push_wms_status,t1.refund_status,t1.warehouse_code';
                if ($item['order_type'] == 0) {
                    $model = new \app\model\Flash(); //闪购
                } else if ($item['order_type'] == 1) {
                    $model = new \app\model\Second(); //秒发
                } else if ($item['order_type'] == 2) {
                    $model = new \app\model\Cross();
                    $field = 't2.main_order_status,t1.predict_time,t2.main_order_no,t1.sub_order_status,t1.sub_order_no,t1.is_delete,t1.is_ts,t1.push_store_status,t1.refund_status';
                } else if ($item['order_type'] == 3) {
                    $model = new \app\model\Tail();
                } else if ($item['order_type'] == 9) {
                    $model = new \app\model\MerchantSecon();
                } else if ($item['order_type'] == 7) {
                    $field = 't2.main_order_status,t1.predict_time,t2.main_order_no,t1.sub_order_status,t1.sub_order_no,t1.is_delete,t1.is_ts,t1.push_wms_status,t1.refund_status,t1.warehouse_id as warehouse_code';
                    $model = new \app\model\Tripartite();
                }
                $model2 = clone $model;

                $order = $model->alias('t1')
                    ->join('order_main t2', 't2.id=t1.main_order_id')
                    ->where('t1.sub_order_no', $item['sub_order_no'])->field($field)->find();
                if (empty($order)) throw new Exception("未找到订单");
                if ($order['sub_order_status'] != 1) throw new Exception("订单不是已支付待发货状态!");
                if ($order['is_delete'] != 0) throw new Exception("订单已删除!");
                if ($order['is_ts'] != 1) throw new Exception("订单不是暂存状态!");
                if ($order['refund_status'] == 2) throw new Exception("订单退款成功!");
                if ($item['order_type'] == 2) {
                    //跨境
                    if ($order['push_store_status'] == 1) throw new Exception("订单已推送发货仓!");
                    $pushData = array(
                        'namespace' => "orders",
                        'key'       => "cross_push_" . $order['main_order_no'],
                        'data'      => base64_encode(json_encode(['main_order_no' => $order['main_order_no']])),
                        'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/crossAutoPush/crossAutoPushDeal',
                        'timeout'   => '1s',
                    );
                } else {
                    //非跨境
                    if ($order['push_wms_status'] == 1) {
                        //同步更新萌芽暂存状态
                        $wms_up_data = [
                            'orderno' => $order['sub_order_no'],//商家订单号
                            'storage' => 0, //是否暂存（0否，1是）
                        ];
                        \Curl::receiptInfo($wms_up_data);
                        $pushData = [];
                    } else {
                        //添加超时任务推送萌芽
                        $pushData = array(
                            'namespace' => "orders",
                            'key'       => "ts_push_" . $order['sub_order_no'],
                            'data'      => base64_encode(json_encode(['sub_order_no' => $order['sub_order_no'], 'order_type' => $order['order_type']])),
                            'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/push/pushWms',
                            'timeout'   => "1s",
                        );
                    }
                }

                if(!empty($pushData)){
                    \Curl::timingAdd($pushData);
                }
                $updata = ['is_ts' => 0];
                if (isset($order['predict_time']) && is_numeric($order['predict_time'])) {
                    $currentTime = time();
                    if ($order['predict_time'] < $currentTime) {
                        $currentHour = date('H', $currentTime);
                        if ($currentHour < 10) {
                            // 当前时间小于16点，设置为今天 23:59:59
                            $updata['predict_time'] = strtotime(date('Y-m-d') . ' 23:59:59');
                        } else {
                            // 当前时间大于等于16点，设置为明天 23:59:59
                            $updata['predict_time'] = strtotime(date('Y-m-d', $currentTime + 86400) . ' 23:59:59');
                        }
                    }
                }
                $res = $model2->where('sub_order_no', $item['sub_order_no'])->update($updata);

                Db::name('order_ts')->where('id', $item['id'])->update([
                    'remarks'     => ($item['remarks'] ?? '') . ' 自动取消暂存成功!',
                    'update_time' => time(),
                    'status'      => 1, //执行失败
                ]);

                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                $msg = '自动取消暂存发货失败' . $e->getMessage() . ' ' . $e->getLine();
                Log::write($msg);
                Db::name('order_ts')->where('id', $item['id'])->update([
                    'remarks'     => ($item['remarks'] ?? '') .' '. $msg,
                    'update_time' => time(),
                    'status'      => 2, //执行失败
                ]);
            }
        }
        return true;
    }

    /**
     * @方法描述: 定时发货
     * <AUTHOR>
     * @Date 2025/6/16 16:40
     * @param $params
     * @return true
     */
    public function scheduledDelivery($params)
    {
        $periods       = Es::name(Es::PERIODS)->where([
            ['is_timing_pushorder', '==', 1],
            ['predict_shipment_time', '>=', date('Y-m-d 00:00:00')],
            ['predict_shipment_time', '<=', date('Y-m-d 23:59:59')],
        ])->field('id,periods_type')->select()->toArray();
        $periods_group = array_group($periods, 'periods_type');
        foreach ($periods_group as $order_type => $periods_g) {
            $period_ids = array_column($periods_g, 'id');
            $model      = null;
            $field      = 't2.main_order_no,t1.sub_order_no,t1.warehouse_code';
            $where      = [
                ['t1.period', 'in', $period_ids], //id
                ['t1.sub_order_status', '=', 1], //待发货
                ['t1.is_delete', '=', 0], //订单未删除
                ['t1.refund_status', '<>', 2], //未退款
            ];
            if ($order_type == 0) {
                $model   = new \app\model\Flash(); //闪购
                $where[] = ['t1.push_wms_status', '=', 0];// 未推送
            } else if ($order_type == 1) {
                $model   = new \app\model\Second(); //秒发
                $where[] = ['t1.push_wms_status', '=', 0];// 未推送
            } else if ($order_type == 2) {
                $model   = new \app\model\Cross();
                $where[] = ['t1.push_store_status', '=', 0];// 未推送
            } else if ($order_type == 3) {
                $model   = new \app\model\Tail();
                $where[] = ['t1.push_wms_status', '=', 0];// 未推送
            } else if ($order_type == 9) {
                $model   = new \app\model\MerchantSecon();
                $where[] = ['t1.push_wms_status', '=', 0];// 未推送
            }

            $orders = $model->alias('t1')
                ->join('order_main t2', 't2.id=t1.main_order_id')
                ->where($where)->column($field);

            foreach ($orders as $order) {
                try {
                    if ($order_type == 2) {
                        $pushData = array(
                            'namespace' => "orders",
                            'key'       => "cross_push_" . $order['main_order_no'],
                            'data'      => base64_encode(json_encode(['main_order_no' => $order['main_order_no']])),
                            'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/crossAutoPush/crossAutoPushDeal',
                            'timeout'   => '1s',
                        );
                    } else {
                        //添加超时任务推送萌芽
                        $pushData = array(
                            'namespace' => "orders",
                            'key'       => "ts_push_" . $order['sub_order_no'],
                            'data'      => base64_encode(json_encode(['sub_order_no' => $order['sub_order_no'], 'order_type' => $order_type])),
                            'callback'  => env('ITEM.ORDERS_URL') . '/orders/v3/push/pushWms',
                            'timeout'   => "1s",
                        );
                    }
                    \Curl::timingAdd($pushData);
                    Log::write('定时发货成功: ' . ($order['sub_order_no'] ?? ''));
                } catch (\Exception $e) {
                    $msg = '定时发货失败' . ($order['sub_order_no'] ?? '') . $e->getMessage() . ' ' . $e->getLine();
                    Log::write($msg);
                }
            }
        }
        return true;
    }


    /**
     * Description:获取用户首单订单
     * Author: gangh
     * Date: 2025/1/8
     * @param $params
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getUserFirstOrder($params)
    {
        return (new OrderModel())->getUserFirstOrder($params['uid']);
    }

}