<?php

namespace app\model;

use think\Model;

class PaymentOperationLogs extends Model
{
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_time';
    protected $updateTime = false; // 日志表不需要更新时间
    
    // 操作类型常量
    const TYPE_PAYMENT = 1; // 收款
    const TYPE_REFUND = 2;  // 退款
    
    /**
     * 获取操作类型名称
     * @param int $type
     * @return string
     */
    public static function getTypeName($type)
    {
        $typeMap = [
            self::TYPE_PAYMENT => '收款',
            self::TYPE_REFUND => '退款'
        ];
        return $typeMap[$type] ?? '未知';
    }
}
