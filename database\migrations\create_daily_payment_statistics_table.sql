-- 每日收款退款统计表
CREATE TABLE `vh_daily_payment_statistics` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `company_code` varchar(10) NOT NULL COMMENT '公司编码(001,002,008,032)',
  `company_name` varchar(100) NOT NULL COMMENT '公司名称',
  `date` date NOT NULL COMMENT '统计日期',
  `payment_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '收款金额',
  `refund_amount` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '退款金额',
  `created_time` int(11) NOT NULL COMMENT '创建时间',
  `updated_time` int(11) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_company_date` (`company_code`, `date`),
  KEY `idx_date` (`date`),
  KEY `idx_company_code` (`company_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='每日收款退款统计表';

-- 操作日志表
CREATE TABLE `vh_payment_operation_logs` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `company_code` varchar(10) NOT NULL COMMENT '公司编码',
  `operation_type` tinyint(1) NOT NULL COMMENT '操作类型(1:收款,2:退款)',
  `amount` decimal(15,2) NOT NULL COMMENT '金额',
  `operation_date` date NOT NULL COMMENT '操作日期',
  `created_time` int(11) NOT NULL COMMENT '创建时间',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  PRIMARY KEY (`id`),
  KEY `idx_company_date` (`company_code`, `operation_date`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收款退款操作日志表';
