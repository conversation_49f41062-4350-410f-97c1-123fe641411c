<?php

namespace app\service;

use app\BaseService;
use app\model\DailyPaymentStatistics;
use think\facade\Db;
use think\facade\Cache;

class PaymentStatistics extends BaseService
{
    /**
     * Redis缓存前缀
     */
    const REDIS_PREFIX = 'payment_statistics:';
    
    /**
     * 写入收款/退款数据
     * @param string $companyCode 公司编码
     * @param int $operationType 操作类型 1:收款 2:退款
     * @param float $amount 金额
     * @return bool
     */
    public function writePaymentData($companyCode, $operationType, $amount)
    {
        try {
            // 验证公司编码
            if (!DailyPaymentStatistics::isValidCompanyCode($companyCode)) {
                $this->throwError('无效的公司编码');
            }

            // 验证操作类型
            if (!in_array($operationType, [1, 2])) {
                $this->throwError('无效的操作类型');
            }

            // 验证金额
            if ($amount <= 0) {
                $this->throwError('金额必须大于0');
            }

            $date = date('Y-m-d');

            // 直接更新Redis缓存
            $this->updateRedisCache($companyCode, $operationType, $amount, $date);

            return true;

        } catch (\Exception $e) {
            // 发送企业微信通知
            $msg = "收款统计写入失败: 公司编码:{$companyCode}, 操作类型:{$operationType}, 金额:{$amount}, 错误:{$e->getMessage()}";
            try {
                \Curl::wecomSend($msg, 'LongFei', 'text');
            } catch (\Exception $notifyException) {
                // 通知失败也记录日志
                \think\facade\Log::error('企业微信通知失败: ' . $notifyException->getMessage());
            }

            $this->throwError($e->getMessage());
        }
    }
    
    /**
     * 更新Redis缓存
     * @param string $companyCode
     * @param int $operationType
     * @param float $amount
     * @param string $date
     */
    private function updateRedisCache($companyCode, $operationType, $amount, $date)
    {
        $redis = $this->getRedisConnection();

        // 构建Redis key
        $key = self::REDIS_PREFIX . $companyCode . ':' . $date;

        // 根据操作类型更新对应字段
        if ($operationType == 1) { // 1=收款
            $redis->hIncrByFloat($key, 'payment_amount', $amount);
        } else { // 2=退款
            $redis->hIncrByFloat($key, 'refund_amount', $amount);
        }

        // 设置过期时间为7天
        $redis->expire($key, 7 * 24 * 3600);
    }
    
    /**
     * 获取Redis连接
     * @return \Redis
     */
    private function getRedisConnection()
    {
        $redis = new \Redis();
        $redis->connect(env('CACHE.HOST'), env('CACHE.PORT'));
        if (env('CACHE.PASSWORD')) {
            $redis->auth(env('CACHE.PASSWORD'));
        }
        $redis->select(env('CACHE.db', 1));
        return $redis;
    }

    /**
     * 获取当日数据（从Redis）
     * @param string|null $companyCode 公司编码，为空则获取所有公司
     * @return array
     */
    public function getTodayData($companyCode = null)
    {
        $redis = $this->getRedisConnection();
        $date = date('Y-m-d');
        $result = [];

        $companyCodes = $companyCode ? [$companyCode] : DailyPaymentStatistics::getAllCompanyCodes();

        foreach ($companyCodes as $code) {
            $key = self::REDIS_PREFIX . $code . ':' . $date;
            $data = $redis->hGetAll($key);

            $result[] = [
                'company_code' => $code,
                'company_name' => DailyPaymentStatistics::getCompanyName($code),
                'date' => $date,
                'payment_amount' => $data['payment_amount'] ?? '0.00',
                'refund_amount' => $data['refund_amount'] ?? '0.00'
            ];
        }

        return $result;
    }

    /**
     * 获取历史数据列表
     * @param array $params 查询参数
     * @return array
     */
    public function getHistoryList($params)
    {
        $page = $params['page'] ?? 1;
        $limit = $params['limit'] ?? 20;
        $companyCode = $params['company_code'] ?? '';
        $startDate = $params['start_date'] ?? '';
        $endDate = $params['end_date'] ?? '';

        $where = [];
        if ($companyCode) {
            $where[] = ['company_code', '=', $companyCode];
        }
        if ($startDate) {
            $where[] = ['date', '>=', $startDate];
        }
        if ($endDate) {
            $where[] = ['date', '<=', $endDate];
        }

        $query = DailyPaymentStatistics::where($where);

        // 获取总数
        $total = $query->count();

        // 获取列表数据
        $list = $query->order('date', 'desc')
                     ->page($page, $limit)
                     ->select()
                     ->toArray();

        // 如果查询当日数据，需要合并Redis中的实时数据
        $today = date('Y-m-d');
        if ((!$startDate || $startDate <= $today) && (!$endDate || $endDate >= $today)) {
            $todayData = $this->getTodayData($companyCode);

            // 检查数据库中是否已有今日数据
            $existingTodayData = [];
            foreach ($list as $item) {
                if ($item['date'] == $today) {
                    $existingTodayData[$item['company_code']] = $item;
                }
            }

            // 合并或添加今日数据
            foreach ($todayData as $todayItem) {
                $code = $todayItem['company_code'];
                if (isset($existingTodayData[$code])) {
                    // 更新现有数据
                    foreach ($list as &$item) {
                        if ($item['date'] == $today && $item['company_code'] == $code) {
                            $item['payment_amount'] = $todayItem['payment_amount'];
                            $item['refund_amount'] = $todayItem['refund_amount'];
                            break;
                        }
                    }
                } else {
                    // 添加新数据
                    array_unshift($list, $todayItem);
                    $total++;
                }
            }
        }

        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit
        ];
    }

    /**
     * 将前一天的Redis数据写入数据库
     * @param string|null $date 指定日期，为空则处理昨天的数据
     * @return bool
     */
    public function syncRedisToDatabase($date = null)
    {
        if (!$date) {
            $date = date('Y-m-d', strtotime('-1 day'));
        }

        $redis = $this->getRedisConnection();
        $companyCodes = DailyPaymentStatistics::getAllCompanyCodes();
        $successCount = 0;
        $errorMessages = [];

        Db::startTrans();
        try {
            foreach ($companyCodes as $companyCode) {
                $key = self::REDIS_PREFIX . $companyCode . ':' . $date;
                $data = $redis->hGetAll($key);

                if (empty($data)) {
                    continue; // 没有数据跳过
                }

                $paymentAmount = $data['payment_amount'] ?? 0;
                $refundAmount = $data['refund_amount'] ?? 0;

                // 如果都为0也跳过
                if ($paymentAmount == 0 && $refundAmount == 0) {
                    continue;
                }

                // 检查是否已存在记录
                $existing = DailyPaymentStatistics::where([
                    'company_code' => $companyCode,
                    'date' => $date
                ])->find();

                if ($existing) {
                    // 更新现有记录
                    $existing->payment_amount = $paymentAmount;
                    $existing->refund_amount = $refundAmount;
                    $existing->save();
                } else {
                    // 创建新记录
                    DailyPaymentStatistics::create([
                        'company_code' => $companyCode,
                        'company_name' => DailyPaymentStatistics::getCompanyName($companyCode),
                        'date' => $date,
                        'payment_amount' => $paymentAmount,
                        'refund_amount' => $refundAmount
                    ]);
                }

                $successCount++;

                // 删除Redis中的数据
                $redis->del($key);
            }

            Db::commit();

            // 发送成功通知
            if ($successCount > 0) {
                $msg = "收款统计数据同步成功: 日期:{$date}, 处理公司数:{$successCount}";
                try {
                    \Curl::wecomSend($msg, 'LongFei', 'text');
                } catch (\Exception $e) {
                    \think\facade\Log::error('企业微信通知失败: ' . $e->getMessage());
                }
            }

            return true;

        } catch (\Exception $e) {
            Db::rollback();

            // 发送失败通知
            $msg = "收款统计数据同步失败: 日期:{$date}, 错误:{$e->getMessage()}";
            try {
                \Curl::wecomSend($msg, 'LongFei', 'text');
            } catch (\Exception $notifyException) {
                \think\facade\Log::error('企业微信通知失败: ' . $notifyException->getMessage());
            }

            throw $e;
        }
    }
}
