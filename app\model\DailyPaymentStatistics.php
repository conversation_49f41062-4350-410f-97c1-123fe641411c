<?php

namespace app\model;

use think\Model;

class DailyPaymentStatistics extends Model
{
    protected $autoWriteTimestamp = true;
    protected $createTime = 'created_time';
    protected $updateTime = 'updated_time';
    
    // 公司编码映射
    const COMPANY_MAP = [
        '001' => '佰酿云酒（重庆）科技有限公司',
        '002' => '重庆云酒佰酿电子商务有限公司',
        '008' => '渝中区微醺酒业商行',
        '032' => '海南一花一世界科技有限公司'
    ];
    
    /**
     * 获取公司名称
     * @param string $companyCode
     * @return string
     */
    public static function getCompanyName($companyCode)
    {
        return self::COMPANY_MAP[$companyCode] ?? '';
    }
    
    /**
     * 验证公司编码是否有效
     * @param string $companyCode
     * @return bool
     */
    public static function isValidCompanyCode($companyCode)
    {
        return isset(self::COMPANY_MAP[$companyCode]);
    }
    
    /**
     * 获取所有公司编码
     * @return array
     */
    public static function getAllCompanyCodes()
    {
        return array_keys(self::COMPANY_MAP);
    }
}
